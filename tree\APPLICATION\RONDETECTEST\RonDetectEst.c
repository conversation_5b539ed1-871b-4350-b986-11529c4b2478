/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           RonDetectEst.c
 **  File Creation Date: 20-May-2022
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         RonDetectEst
 **  Model Description:  This software component performs the evaluation of ron level (i.e. signals RonLevelUsed and RonLevelEE)
 **  Model Version:      1.1107
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Fri May 20 08:34:31 2022
 **
 **  Last Saved Modification:  RoccaG - Fri May 20 08:09:49 2022
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "RonDetectEst_out.h"
#include "RonDetectEst_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKRONSUSPPOS_dim               4U                        /* Referenced by: '<S22>/BKRONSUSPPOS_dim' */

/* Size for breakpoint BKRONSUSPPOS (-1) */
#define BKSARON_dim                    8U                        /* Referenced by:
                                                                  * '<S14>/BKSARON_dim'
                                                                  * '<S21>/BKSARON_dim'
                                                                  */

/* Size for breakpoint BKSARON (-1) */
#define ID_VER_RONDETECTEST_DEF        11107U                    /* Referenced by: '<S5>/Constant10' */

/* Model Version. */
#define MAX_CNT_RONDETECT              254U                      /* Referenced by:
                                                                  * '<S13>/VT_RON_SIZE1'
                                                                  * '<S13>/VT_RON_SIZE2'
                                                                  * '<S13>/VT_RON_SIZE3'
                                                                  */

/* Maximum allowed value for test-run counters in fast strategy. */
#define MAX_INT32                      2147483647U               /* Referenced by:
                                                                  * '<S15>/Constant'
                                                                  * '<S15>/Constant1'
                                                                  */

/* Maximum value for type int32 */
#define MAX_INT8                       2032                      /* Referenced by:
                                                                  * '<S15>/Constant4'
                                                                  * '<S15>/Constant5'
                                                                  */

/* Maximum value for type int8 */
#define MAX_UINT16                     65535U                    /* Referenced by: '<S8>/BackGround_Mode_Chart' */

/* Maximum value for type uint16 */
#define MAX_UINT16_SIGNED              65535                     /* Referenced by: '<S15>/Constant2' */

/* Maximum value for type uint16 (with int32 type) */
#define MIN_RONLEVEL                   0U                        /* Referenced by:
                                                                  * '<S8>/BackGround_Mode_Chart'
                                                                  * '<S13>/MIN_RONLEVEL'
                                                                  * '<S13>/MIN_RONLEVEL1'
                                                                  */

/* Minimum allowed level for ron detection. */
#define VT_RON_SIZE                    20U                       /* Referenced by: '<S13>/VT_RON_SIZE' */

/* Maximum number of collected run for ron level monitoring */
#define VT_RON_SUSP_SIZE               30U                       /* Referenced by: '<S8>/BackGround_Mode_Chart' */

/* Maximum number of background runs to be analized for ron level change */
#define VT_RON_SUSP_TEST_SIZE          10U                       /* Referenced by: '<S8>/BackGround_Mode_Chart' */

/* Maximum number of background runs to be analized for ron level change */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_RONDETECTEST_

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Exported calibration memory section */
/*Init of exported calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_EXPORT_CALIBRATION */
CALQUAL CALQUAL_POST uint16_T BKLOADSARON[9] = { 3840U, 4480U, 5120U, 5760U,
  6400U, 7040U, 7680U, 8320U, 8960U } ;/* Referenced by: '<S12>/BKLOADSARON' */

/* Breakpoints of load for ron SARon calculation */

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRONSUSPPOS[5] = { 0U, 1U, 3U, 5U, 6U
} ;                                    /* Referenced by: '<S22>/BKRONSUSPPOS' */

/* Breakpoints of CntRonSuspPos */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKSARON[9] = { -128, -112, -96, -80,
  -64, -48, -32, -16, 0 } ;            /* Referenced by:
                                        * '<S14>/BKSARON'
                                        * '<S21>/BKSARON'
                                        */

/* SATableRon bkp */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T CNTRONDEC = 1U;/* Referenced by: '<S13>/CNTRONDEC' */

/* CntRonMinDec threshold */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T CNTRONDETECT = 2U;/* Referenced by: '<S13>/CNTRONDETECT' */

/* CntRonRun threshold */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T CNTRONDETECT0 = 2U;
                                      /* Referenced by: '<S13>/CNTRONDETECT0' */

/* CntDRon0 threshold to finish test */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T CNTRONINC = 1U;/* Referenced by: '<S13>/CNTRONINC' */

/* CntRonMaxInc threshold */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T FORCEDRONLEVEL = 255U;/* Referenced by:
                                                                * '<S3>/Constant'
                                                                * '<S8>/Constant1'
                                                                * '<S10>/Constant1'
                                                                * '<S11>/Constant1'
                                                                */

/* RonLevel forced value (only if >=0 and <=4) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T MAX_RONLEVEL = 4U;/* Referenced by:
                                                            * '<S8>/CNTRONDETECT2'
                                                            * '<S13>/CNTRONDETECT1'
                                                            * '<S13>/CNTRONDETECT2'
                                                            */

/* Maximum allowed value for RON level estimation */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T RONLEVELREC = 2U;/* Referenced by: '<S6>/Constant' */

/* Ron level during recovery */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T RONSAMPLES = 2U;/* Referenced by: '<S3>/Constant1' */

/* Number of samples to calculate RonLevel */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T RONSUSPRUN = 30U;/* Referenced by: '<S8>/Constant2' */

/* Number of background run to analize for ron level change */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T TBDRONLEVEL[54] = { 0, 0, 1, 2, 2, 2, 0,
  0, 1, 1, 2, 2, 0, 0, 1, 1, 2, 2, 0, 0, 0, 1, 1, 2, 0, 0, 0, 1, 1, 1, 0, 0, 0,
  1, 1, 1, 0, -1, 0, 1, 1, 1, 0, -1, 0, 0, 1, 1, -1, -1, 0, 0, 0, 0 } ;/* Referenced by:
                                                                      * '<S14>/TBDRONLEVEL'
                                                                      * '<S21>/TBDRONLEVEL'
                                                                      */

/* RonLevel correction table */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTRONSUSPNEGD[5] = { 5U, 6U, 10U, 10U,
  10U } ;                      /* Referenced by: '<S8>/BackGround_Mode_Chart' */

/* CntRonSuspNeg lower threshold to enable ron level Decrease */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTRONSUSPNEGI[5] = { 0U, 1U, 1U, 3U, 4U
} ;                            /* Referenced by: '<S8>/BackGround_Mode_Chart' */

/* CntRonSuspNeg higher threshold to enable ron level Increase */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T CntDRon0;                      /* '<S9>/Merge2' */

/* Number of run without RonLevel correction */
uint8_T CntRonMaxInc;                  /* '<S9>/Merge3' */

/* Run completed with RonLevel = MAX and positive correction */
uint8_T CntRonMinDec;                  /* '<S9>/Merge4' */

/* Run completed with RonLevel = 0 and negative correction */
uint8_T CntRonRun;                     /* '<S9>/Merge5' */

/* RON test counter */
uint8_T CntRonSuspNeg;                 /* '<S9>/Merge6' */

/* Number of background run stopped with negative ron changes */
uint8_T CntRonSuspPos;                 /* '<S9>/Merge7' */

/* Number of background run stopped with positive ron changes */
uint8_T CntRonSuspZero;                /* '<S9>/Merge8' */

/* Number of background run with neutral ron changes */
uint8_T FlgRonDetect;                  /* '<S9>/Merge1' */

/* Flag that declares ron detection */
uint8_T FlgRonMaxInc;                  /* '<S9>/Merge9' */

/* CntRonMaxInc over threshold */
uint8_T FlgRonMinDec;                  /* '<S9>/Merge10' */

/* CntRonMinDec over threshold */
//uint8_T RonLevelUsed;   
uint8_T RonLevelUsed_EstEOA;                /* '<S9>/Merge' */
uint8_T RonLevel_In;
/* RON level used for SARon calculation */
int8_T VtDRonLevelSusp[10];            /* '<S9>/Merge11' */

/* RonLevel changing history in background mode */
int8_T VtDRonLevelSuspRun[30];         /* '<S9>/Merge12' */

/* RonLevel vector correction in background mode */
uint8_T VtRonLevel[20];                /* '<S9>/Merge13' */
uint8_T RonLevelEE_FastTestPoint;
uint8_T  RonLevelEE_FastTestValue;
/* RonLevel vector for each run */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT int8_T DRonLevel;    /* '<S9>/Merge14' */

/* RonLevel correction for last run */
STATIC_TEST_POINT uint8_T IDDRonLevelSusp;/* '<S9>/Merge15' */

/* VtDRonLevelSusp index */
STATIC_TEST_POINT uint8_T IDSuspPos;   /* '<S9>/Merge16' */

/* BKRONSUSPPOS index */
STATIC_TEST_POINT uint32_T IdVer_RonDetectEst;/* '<S5>/Constant10' */

/* Model Version */
STATIC_TEST_POINT uint8_T MaxCntRonSuspNeg;/* '<S9>/Merge17' */

/* Max potential number of background run with negative ron changes */
STATIC_TEST_POINT uint32_T SecStartRun;/* '<S9>/Merge18' */

/* SecondsWorkTime at the beginning of current run */
STATIC_TEST_POINT int8_T VtDRonLevel[20];/* '<S9>/Merge19' */

/* RonLevel vector correction for each run */
STATIC_TEST_POINT uint8_T VtRonLoad[20];/* '<S9>/Merge20' */

/* Load for each run */
STATIC_TEST_POINT uint16_T VtRonRpm[20];/* '<S9>/Merge21' */

/* Rpm for each run */
STATIC_TEST_POINT uint16_T VtRonSec[20];/* '<S9>/Merge22' */

/* Duration of each run */
STATIC_TEST_POINT int8_T VtRonTAir[20];/* '<S9>/Merge23' */

/* TAir for each run */
STATIC_TEST_POINT int8_T VtRonTWater[20];/* '<S9>/Merge24' */

/* TWater for each run */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Model step function */
void RonDetectEst_100ms(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_100ms' incorporates:
   *  SubSystem: '<Root>/T100ms'
   *
   * Block description for '<Root>/T100ms':
   *  This block merges the ron level estimation performed by software component RonDetectFuel, with the one perfomed by this software component.
   *  This function call shall be used only if software component RonDetectFuel has been integrated.
   *
   * Block requirements for '<Root>/T100ms':
   *  1. EISB_FCA6CYL_SW_REQ_2816: At each task 100ms, software shall set the signal used to store RO... (ECU_SW_Requirements#12230)
   */
  /* Switch: '<S10>/Switch' incorporates:
   *  Constant: '<S10>/CNTRONDETECT2'
   *  Constant: '<S10>/Constant1'
   *  Inport: '<Root>/RonLevelFuel'
   *  RelationalOperator: '<S10>/RelationalOperator'
   */
  if (FORCEDRONLEVEL <= ((uint8_T)MAX_RONLEVEL_EXP)) {
    RonLevelEE = FORCEDRONLEVEL;
  } else {
    RonLevelEE = RonLevelFuel;
  }

  /* End of Switch: '<S10>/Switch' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_100ms' */
}

/* Model step function */
void RonDetectEst_5ms(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_5ms' incorporates:
   *  SubSystem: '<Root>/T5ms'
   *
   * Block description for '<Root>/T5ms':
   *  This block merges the ron level estimation performed by software component RonDetectCross, with the one perfomed by this software component.
   *  This function call shall be used only if software component RonDetectCross has been integrated.
   *
   * Block requirements for '<Root>/T5ms':
   *  1. EISB_FCA6CYL_SW_REQ_2817: At each task 5ms, software shall set the signal used to store RON ... (ECU_SW_Requirements#12231)
   */
  /* Switch: '<S11>/Switch' incorporates:
   *  Constant: '<S11>/CNTRONDETECT2'
   *  Constant: '<S11>/Constant1'
   *  Inport: '<Root>/RonLevelCross'
   *  RelationalOperator: '<S11>/RelationalOperator'
   */
  if (FORCEDRONLEVEL <= ((uint8_T)MAX_RONLEVEL_EXP)) {
    RonLevelEE = FORCEDRONLEVEL;
  } else {
    RonLevelEE = RonLevelCross;
  }

  /* End of Switch: '<S11>/Switch' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_5ms' */
}

/* Model step function */
void RonDetectEst_BackgroundInit(void)
{
  int32_T i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_BackgroundInit' incorporates:
   *  SubSystem: '<Root>/BackgroundInit'
   *
   * Block description for '<Root>/BackgroundInit':
   *  This block makes available a function call that can be used to reset
   *  "background" function's signals (usually called by software component
   *  RonDetectMgm).
   *
   * Block requirements for '<Root>/BackgroundInit':
   *  1. EISB_FCA6CYL_SW_REQ_2814: Software shall provide a reset functionality named RonDetectEst_Ba... (ECU_SW_Requirements#12228)
   */
  /* SignalConversion generated from: '<S1>/VtDRonLevelSuspRun' */
  for (i = 0; i < 30; i++) {
    VtDRonLevelSuspRun[(i)] = MAX_int8_T;
  }

  /* End of SignalConversion generated from: '<S1>/VtDRonLevelSuspRun' */

  /* SignalConversion generated from: '<S1>/VtDRonLevelSusp' */
  for (i = 0; i < 10; i++) {
    VtDRonLevelSusp[(i)] = MAX_int8_T;
  }

  /* End of SignalConversion generated from: '<S1>/VtDRonLevelSusp' */

  /* SignalConversion generated from: '<S1>/CntRonSuspNeg' incorporates:
   *  Constant: '<S1>/Constant5'
   */
  CntRonSuspNeg = 0U;

  /* SignalConversion generated from: '<S1>/CntRonSuspPos' incorporates:
   *  Constant: '<S1>/Constant6'
   */
  CntRonSuspPos = 0U;

  /* SignalConversion generated from: '<S1>/CntRonSuspZero' incorporates:
   *  Constant: '<S1>/Constant7'
   */
  CntRonSuspZero = 0U;

  /* SignalConversion generated from: '<S1>/IDDRonLevelSusp' incorporates:
   *  Constant: '<S1>/Constant13'
   */
  IDDRonLevelSusp = 0U;

  /* SignalConversion generated from: '<S1>/MaxCntRonSuspNeg' incorporates:
   *  Constant: '<S1>/Constant15'
   */
  MaxCntRonSuspNeg = 0U;

  /* SignalConversion generated from: '<S1>/CntRonSuspRunEE' incorporates:
   *  Constant: '<S1>/ZERO1'
   */
  CntRonSuspRunEE = 0U;

  /* SignalConversion generated from: '<S1>/IDRonSuspRunEE' incorporates:
   *  Constant: '<S1>/ZERO'
   */
  IDRonSuspRunEE = 0U;

  /* SignalConversion generated from: '<S1>/CntRonSuspEE' incorporates:
   *  Constant: '<S1>/ZERO2'
   */
  CntRonSuspEE = 0U;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_BackgroundInit' */
}

/* Model step function */
void RonDetectEst_EOA(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_EOA' incorporates:
   *  SubSystem: '<Root>/EOA'
   *
   * Block description for '<Root>/EOA':
   *  This block merges the ron level estimation performed by software component RonDetectFuel, with the one perfomed by this software component.
   *  This function call shall be used only if software component RonDetectFuel has been integrated.
   *
   * Block requirements for '<Root>/EOA':
   *  1. EISB_FCA6CYL_SW_REQ_2820: At each task EOA, software shall set the signal RonLevelUsed (i.e.... (ECU_SW_Requirements#12234)
   */
  /* Switch: '<S12>/Switch' incorporates:
   *  Constant: '<S12>/BKLOADSARON'
   *  Constant: '<S12>/Constant'
   *  Inport: '<Root>/Load'
   *  RelationalOperator: '<S12>/Less Than'
   *  SignalConversion generated from: '<S2>/RonLevelEE'
   */
  //test2
  /* RonLevelUsed assignment removed - managed by RonDetectFuel to prevent oscillation */
  // Removed RonLevelUsed assignment - now fully managed by RonDetectFuel.c
   if (Load > BKLOADSARON[0]) {
    RonLevelUsed = RonLevelEE;
    RonLevelUsed_EstEOA = 2U;
   } else {
     RonLevelUsed = 0U;
     RonLevelUsed_EstEOA = 1U;
   }

  /* End of Switch: '<S12>/Switch' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_EOA' */
}

/* Model step function */
void RonDetectEst_Fast(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_S16_o2_g;
  uint16_T rtb_PreLookUpIdSearch_S16_o1_j;
  uint8_T idx;
  uint8_T nsamp;
  int16_T rtb_Divide;
  boolean_T rtb_RelationalOperator;
  boolean_T rtb_RelationalOperator1;
  boolean_T rtb_RelationalOperator5;
  boolean_T rtb_FlgRonDetect_c;
  int8_T u1;
  int8_T u1_0;
  int32_T u0;
  uint8_T u1_1;
  uint32_T tmp;
  int32_T u1_tmp;
  int32_T VtRonTAir_tmp;
  int32_T VtRonTAir_tmp_tmp;

  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_Fast' incorporates:
   *  SubSystem: '<Root>/FAST_DETECTION'
   *
   * Block description for '<Root>/FAST_DETECTION':
   *  This subsystem implements "monitoring function" which is called to
   *  estimate ron level with a fast test, and is called by software
   *  component RonDetectMgm.
   */
  /* DataTypeConversion: '<S17>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/SATableRonArg'
   */
  rtb_Divide = SATableRonArg;

  /* S-Function (PreLookUpIdSearch_S16): '<S17>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S14>/BKSARON'
   *  Constant: '<S14>/BKSARON_dim'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1_j,
                        &rtb_PreLookUpIdSearch_S16_o2_g, rtb_Divide, &BKSARON[0],
                        ((uint8_T)BKSARON_dim));

  /* Selector: '<S14>/Selector4' incorporates:
   *  Constant: '<S14>/TBDRONLEVEL'
   *  Inport: '<Root>/CntKIntIdxRonArg'
   *
   * Block requirements for '<S14>/TBDRONLEVEL':
   *  1. EISB_FCA6CYL_SW_REQ_2822: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12236)
   */
  u1_tmp = (6 * ((int32_T)rtb_PreLookUpIdSearch_S16_o1_j)) + ((int32_T)
    CntKIntIdxRonArg);

  /* MinMax: '<S13>/MinMax' incorporates:
   *  Constant: '<S13>/CNTRONDETECT1'
   *  Constant: '<S14>/TBDRONLEVEL'
   *  DataTypeConversion: '<S13>/Conversion5'
   *  Selector: '<S14>/Selector4'
   *  SignalConversion generated from: '<S3>/RonLevelEE_old'
   *  Sum: '<S13>/Sum'
   *
   * Block requirements for '<S14>/TBDRONLEVEL':
   *  1. EISB_FCA6CYL_SW_REQ_2822: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12236)
   */
  u1 = (int8_T)(((int32_T)TBDRONLEVEL[(u1_tmp)]) + ((int32_T)RonLevelEE));
  RonLevelUsed = RonLevelFuel;
  if (((int8_T)MAX_RONLEVEL) < u1) {
    u1 = (int8_T)MAX_RONLEVEL;
  }

  /* End of MinMax: '<S13>/MinMax' */

  /* DataTypeConversion: '<S13>/Conversion6' incorporates:
   *  Constant: '<S13>/MIN_RONLEVEL'
   */
  u1_0 = (int8_T)((uint8_T)MIN_RONLEVEL);

  /* MinMax: '<S13>/MinMax1' */
  if (u1 > u1_0) {
    RonLevel_In = (uint8_T)u1;
    RonLevelUsed = RonLevelFuel;
    
  }else {
    RonLevel_In = (uint8_T)u1_0;
    RonLevelUsed = RonLevelFuel;
  }

  /* End of MinMax: '<S13>/MinMax1' */

  /* Sum: '<S13>/Add' incorporates:
   *  Constant: '<S13>/ONE'
   *  SignalConversion generated from: '<S3>/CntRonRun_old'
   *
   * Block requirements for '<S13>/Add':
   *  1. EISB_FCA6CYL_SW_REQ_2824: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12238)
   */
  CntRonRun = (uint8_T)(((uint32_T)CntRonRun) + 1U);

  /* MinMax: '<S13>/MinMax2' incorporates:
   *  Constant: '<S13>/VT_RON_SIZE'
   */
  if (CntRonRun >= ((uint8_T)VT_RON_SIZE)) {
    /* Sum: '<S13>/Add'
     *
     * Block requirements for '<S13>/Add':
     *  1. EISB_FCA6CYL_SW_REQ_2824: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12238)
     */
    CntRonRun = ((uint8_T)VT_RON_SIZE);
  }

  /* RelationalOperator: '<S13>/Relational Operator' incorporates:
   *  Constant: '<S13>/CNTRONDETECT'
   *  MinMax: '<S13>/MinMax2'
   */
  rtb_RelationalOperator = (CntRonRun >= CNTRONDETECT);

  /* Switch: '<S13>/Switch' incorporates:
   *  Constant: '<S13>/ZERO5'
   *  Constant: '<S14>/TBDRONLEVEL'
   *  RelationalOperator: '<S13>/Relational Operator8'
   *  Selector: '<S14>/Selector4'
   *
   * Block requirements for '<S14>/TBDRONLEVEL':
   *  1. EISB_FCA6CYL_SW_REQ_2822: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12236)
   */
  if (TBDRONLEVEL[(u1_tmp)] != 0) {
    /* Sum: '<S13>/Add1' incorporates:
     *  Constant: '<S13>/ZERO'
     *
     * Block requirements for '<S13>/Add1':
     *  1. EISB_FCA6CYL_SW_REQ_2825: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12239)
     */
    CntDRon0 = 0U;
  } else {
    /* Sum: '<S13>/Add1' incorporates:
     *  Constant: '<S13>/ONE1'
     *  SignalConversion generated from: '<S3>/CntDRon0_old'
     *
     * Block requirements for '<S13>/Add1':
     *  1. EISB_FCA6CYL_SW_REQ_2825: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12239)
     */
    CntDRon0 = (uint8_T)(((uint32_T)CntDRon0) + 1U);

    /* MinMax: '<S13>/MinMax3' incorporates:
     *  Constant: '<S13>/VT_RON_SIZE1'
     */
    if (CntDRon0 >= ((uint8_T)MAX_CNT_RONDETECT)) {
      /* Sum: '<S13>/Add1'
       *
       * Block requirements for '<S13>/Add1':
       *  1. EISB_FCA6CYL_SW_REQ_2825: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12239)
       */
      CntDRon0 = ((uint8_T)MAX_CNT_RONDETECT);
    }

    /* End of MinMax: '<S13>/MinMax3' */
  }

  /* End of Switch: '<S13>/Switch' */

  /* RelationalOperator: '<S13>/Relational Operator1' incorporates:
   *  Constant: '<S13>/CNTRONDETECT0'
   */
  rtb_RelationalOperator1 = (CntDRon0 >= CNTRONDETECT0);

  /* Switch: '<S13>/Switch2' incorporates:
   *  Constant: '<S13>/CNTRONDETECT2'
   *  Constant: '<S13>/ZERO4'
   *  Constant: '<S14>/TBDRONLEVEL'
   *  Logic: '<S13>/Logical Operator3'
   *  RelationalOperator: '<S13>/Relational Operator4'
   *  RelationalOperator: '<S13>/Relational Operator7'
   *  Selector: '<S14>/Selector4'
   *  SignalConversion generated from: '<S3>/RonLevelEE_old'
   *
   * Block requirements for '<S14>/TBDRONLEVEL':
   *  1. EISB_FCA6CYL_SW_REQ_2822: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12236)
   */
  if ((TBDRONLEVEL[(u1_tmp)] > 0) && (RonLevelEE == MAX_RONLEVEL)) {
    /* Sum: '<S13>/Add4' incorporates:
     *  Constant: '<S13>/ONE4'
     *  SignalConversion generated from: '<S3>/CntRonMaxInc_old'
     *
     * Block requirements for '<S13>/Add4':
     *  1. EISB_FCA6CYL_SW_REQ_2827: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12241)
     */
    CntRonMaxInc = (uint8_T)(((uint32_T)CntRonMaxInc) + 1U);

    /* MinMax: '<S13>/MinMax5' incorporates:
     *  Constant: '<S13>/VT_RON_SIZE3'
     */
    if (CntRonMaxInc >= ((uint8_T)MAX_CNT_RONDETECT)) {
      /* Sum: '<S13>/Add4'
       *
       * Block requirements for '<S13>/Add4':
       *  1. EISB_FCA6CYL_SW_REQ_2827: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12241)
       */
      CntRonMaxInc = ((uint8_T)MAX_CNT_RONDETECT);
    }

    /* End of MinMax: '<S13>/MinMax5' */
  } else {
    /* Sum: '<S13>/Add4' incorporates:
     *  Constant: '<S13>/ZERO2'
     *
     * Block requirements for '<S13>/Add4':
     *  1. EISB_FCA6CYL_SW_REQ_2827: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12241)
     */
    CntRonMaxInc = 0U;
  }

  /* End of Switch: '<S13>/Switch2' */

  /* RelationalOperator: '<S13>/Relational Operator5' incorporates:
   *  Constant: '<S13>/CNTRONINC'
   */
  rtb_RelationalOperator5 = (CntRonMaxInc >= CNTRONINC);

  /* Switch: '<S13>/Switch1' incorporates:
   *  Constant: '<S13>/MIN_RONLEVEL1'
   *  Constant: '<S13>/ZERO3'
   *  Constant: '<S14>/TBDRONLEVEL'
   *  Logic: '<S13>/Logical Operator1'
   *  RelationalOperator: '<S13>/Relational Operator2'
   *  RelationalOperator: '<S13>/Relational Operator6'
   *  Selector: '<S14>/Selector4'
   *  SignalConversion generated from: '<S3>/RonLevelEE_old'
   *
   * Block requirements for '<S14>/TBDRONLEVEL':
   *  1. EISB_FCA6CYL_SW_REQ_2822: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12236)
   */
  if ((TBDRONLEVEL[(u1_tmp)] < 0) && (RonLevelEE == ((uint8_T)MIN_RONLEVEL))) {
    /* Sum: '<S13>/Add2' incorporates:
     *  Constant: '<S13>/ONE2'
     *  SignalConversion generated from: '<S3>/CntRonMinDec_old'
     *
     * Block requirements for '<S13>/Add2':
     *  1. EISB_FCA6CYL_SW_REQ_2826: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12240)
     */
    CntRonMinDec = (uint8_T)(((uint32_T)CntRonMinDec) + 1U);

    /* MinMax: '<S13>/MinMax4' incorporates:
     *  Constant: '<S13>/VT_RON_SIZE2'
     */
    if (CntRonMinDec >= ((uint8_T)MAX_CNT_RONDETECT)) {
      /* Sum: '<S13>/Add2'
       *
       * Block requirements for '<S13>/Add2':
       *  1. EISB_FCA6CYL_SW_REQ_2826: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12240)
       */
      CntRonMinDec = ((uint8_T)MAX_CNT_RONDETECT);
    }

    /* End of MinMax: '<S13>/MinMax4' */
  } else {
    /* Sum: '<S13>/Add2' incorporates:
     *  Constant: '<S13>/ZERO1'
     *
     * Block requirements for '<S13>/Add2':
     *  1. EISB_FCA6CYL_SW_REQ_2826: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12240)
     */
    CntRonMinDec = 0U;
  }

  /* End of Switch: '<S13>/Switch1' */

  /* RelationalOperator: '<S13>/Relational Operator3' incorporates:
   *  Constant: '<S13>/CNTRONDEC'
   */
  rtb_FlgRonDetect_c = (CntRonMinDec >= CNTRONDEC);

  /* MinMax: '<S15>/MinMax4' incorporates:
   *  Constant: '<S15>/Constant4'
   *  Inport: '<Root>/TAir'
   */
  if (TAir < ((int16_T)MAX_INT8)) {
    rtb_Divide = TAir;
  } else {
    rtb_Divide = ((int16_T)MAX_INT8);
  }

  /* End of MinMax: '<S15>/MinMax4' */

  /* Chart: '<S3>/RonLevel_selector' incorporates:
   *  MinMax: '<S13>/MinMax2'
   */
  VtRonTAir_tmp_tmp = ((int32_T)CntRonRun) - 1;

  /* SignalConversion generated from: '<S3>/VtRonTAir' incorporates:
   *  Chart: '<S3>/RonLevel_selector'
   *  DataTypeConversion: '<S15>/Conversion4'
   *  MinMax: '<S15>/MinMax3'
   *  Product: '<S18>/Divide'
   *  SignalConversion generated from: '<S3>/VtDRonLevel'
   *  SignalConversion generated from: '<S3>/VtRonLoad'
   *  SignalConversion generated from: '<S3>/VtRonRpm'
   *  SignalConversion generated from: '<S3>/VtRonSec'
   *  SignalConversion generated from: '<S3>/VtRonTWater'
   */
  VtRonTAir_tmp = (int32_T)((uint8_T)VtRonTAir_tmp_tmp);
  VtRonTAir[(VtRonTAir_tmp)] = (int8_T)(rtb_Divide / 16);

  /* MinMax: '<S15>/MinMax5' incorporates:
   *  Constant: '<S15>/Constant5'
   *  Inport: '<Root>/TWater'
   */
  if (TWater < ((int16_T)MAX_INT8)) {
    rtb_Divide = TWater;
  } else {
    rtb_Divide = ((int16_T)MAX_INT8);
  }

  /* End of MinMax: '<S15>/MinMax5' */

  /* Product: '<S19>/Divide' */
  rtb_Divide = (int16_T)(rtb_Divide / 16);

  /* Chart: '<S3>/RonLevel_selector' incorporates:
   *  Constant: '<S14>/TBDRONLEVEL'
   *  DataTypeConversion: '<S13>/Conversion'
   *  DataTypeConversion: '<S15>/Conversion3'
   *  DataTypeConversion: '<S15>/Conversion5'
   *  Inport: '<Root>/Load'
   *  Inport: '<Root>/Rpm'
   *  Selector: '<S14>/Selector4'
   *  SignalConversion generated from: '<S3>/VtDRonLevel'
   *  SignalConversion generated from: '<S3>/VtRonLoad'
   *  SignalConversion generated from: '<S3>/VtRonRpm'
   *  SignalConversion generated from: '<S3>/VtRonTWater'
   *
   * Block requirements for '<S14>/TBDRONLEVEL':
   *  1. EISB_FCA6CYL_SW_REQ_2822: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12236)
   */
  /* Gateway: FAST_DETECTION/RonLevel_selector */
  /* During: FAST_DETECTION/RonLevel_selector */
  /* This Stateflow collects engine state for each run of fast detection test and assign the new estimated ron level at the end of the test. */
  /* Entry Internal: FAST_DETECTION/RonLevel_selector */
  /* Transition: '<S16>:1' */
  /*  Assign output to old value for better code generation  */
  /* Transition: '<S16>:17' */
  /*  Input log  */
  VtRonLevel[(VtRonTAir_tmp)] = (uint8_T)RonLevel_In;
  VtDRonLevel[(VtRonTAir_tmp)] = TBDRONLEVEL[(u1_tmp)];
  VtRonRpm[(VtRonTAir_tmp)] = Rpm;
  VtRonTWater[(VtRonTAir_tmp)] = (int8_T)rtb_Divide;
  VtRonLoad[(VtRonTAir_tmp)] = (uint8_T)(((uint32_T)Load) >> ((uint32_T)8));

  /* MinMax: '<S15>/MinMax' incorporates:
   *  Constant: '<S15>/Constant'
   *  Inport: '<Root>/SecondsWorkTime'
   */
  if (SecondsWorkTime < MAX_INT32) {
    tmp = SecondsWorkTime;
  } else {
    tmp = MAX_INT32;
  }

  /* End of MinMax: '<S15>/MinMax' */

  /* MinMax: '<S15>/MinMax1' incorporates:
   *  Constant: '<S15>/Constant1'
   *  SignalConversion generated from: '<S3>/SecStartRun_old'
   */
  if (SecStartRun >= MAX_INT32) {
    SecStartRun = MAX_INT32;
  }

  /* Sum: '<S15>/Plus' incorporates:
   *  DataTypeConversion: '<S15>/Conversion'
   *  DataTypeConversion: '<S15>/Conversion1'
   */
  u0 = ((int32_T)tmp) - ((int32_T)SecStartRun);

  /* MinMax: '<S15>/MinMax2' incorporates:
   *  Constant: '<S15>/Constant2'
   */
  if (u0 >= MAX_UINT16_SIGNED) {
    u0 = MAX_UINT16_SIGNED;
  }

  /* MinMax: '<S15>/MinMax3' incorporates:
   *  MinMax: '<S15>/MinMax2'
   */
  if (u0 > 0) {
    /* Chart: '<S3>/RonLevel_selector' incorporates:
     *  SignalConversion generated from: '<S3>/VtRonSec'
     */
    VtRonSec[(VtRonTAir_tmp)] = (uint16_T)u0;
  } else {
    /* Chart: '<S3>/RonLevel_selector' incorporates:
     *  SignalConversion generated from: '<S3>/VtRonSec'
     */
    VtRonSec[(VtRonTAir_tmp)] = 0U;
  }

  /* Chart: '<S3>/RonLevel_selector' incorporates:
   *  Constant: '<S3>/Constant'
   *  Constant: '<S3>/Constant1'
   *  DataTypeConversion: '<S13>/Conversion'
   *  DataTypeConversion: '<S13>/Conversion2'
   *  DataTypeConversion: '<S13>/Conversion3'
   *  MinMax: '<S13>/MinMax2'
   */
  if ((((rtb_RelationalOperator1 || rtb_FlgRonDetect_c) ||
        rtb_RelationalOperator5) || (!rtb_RelationalOperator)) || (((int32_T)
        RONSAMPLES) <= 1)) {
    /* SignalConversion generated from: '<S3>/RonLevelEE' incorporates:
     *  DataTypeConversion: '<S13>/Conversion'
     */
    /* Transition: '<S16>:15' */
    /* Transition: '<S16>:87' */
    /* Transition: '<S16>:85' */
    /* Transition: '<S16>:89' */
    /* Transition: '<S16>:90' */
    /* Transition: '<S16>:95' */
    /* Transition: '<S16>:75'
     * Requirements for Transition: '<S16>:75':
     *  1. EISB_FCA6CYL_SW_REQ_2828: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12242)
     */
    RonLevelEE = RonLevel_In;
    //RonLevelUsed = RonLevelFuel;

    /* Transition: '<S16>:108' */
  } else {
     
    /* Transition: '<S16>:80' */
    /* Transition: '<S16>:83' */
    /* Transition: '<S16>:92' */
    if (CntRonRun < RONSAMPLES) {
      nsamp = CntRonRun;
    } else {
      nsamp = RONSAMPLES;
    }

    nsamp = (uint8_T)((int32_T)(((int32_T)nsamp) - 1));
    if (((int32_T)nsamp) == 0) {
      /* SignalConversion generated from: '<S3>/RonLevelEE' incorporates:
       *  DataTypeConversion: '<S13>/Conversion'
       */
      /* Transition: '<S16>:94' */
      /* Transition: '<S16>:75'
       * Requirements for Transition: '<S16>:75':
       *  1. EISB_FCA6CYL_SW_REQ_2828: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12242)
       */
     RonLevelEE = RonLevel_In;
      //test7
     RonLevelFuel = RonLevelEE;

      /* Transition: '<S16>:108' */
    } else {
      /* Transition: '<S16>:97' */
      idx = 0U;
      
      RonLevelEE = RonLevel_In;
     
      RonLevelFuel = RonLevelEE;
      while (idx <= nsamp) {
        /* Transition: '<S16>:99' */
        /* Transition: '<S16>:101' */
        u1_1 = VtRonLevel[VtRonTAir_tmp_tmp - ((int32_T)idx)];
        if (RonLevelEE <= u1_1) {
          RonLevelEE = u1_1;
          RonLevelFuel = RonLevelEE;
        }

        /* Transition: '<S16>:102' */
        idx = (uint8_T)((int32_T)(((int32_T)idx) + 1));
      }

      /* Transition: '<S16>:104'
       * Requirements for Transition: '<S16>:104':
       *  1. EISB_FCA6CYL_SW_REQ_2829: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12243)
       */
    }
  }

  /* Transition: '<S16>:110' */
  if (FORCEDRONLEVEL <= ((uint8_T)MAX_RONLEVEL_EXP)) {
    /* SignalConversion generated from: '<S3>/RonLevelEE' */
    /* Transition: '<S16>:115' */
    /* Transition: '<S16>:117' */
    RonLevelEE = FORCEDRONLEVEL;
    RonLevelFuel = RonLevelEE;

    /* Transition: '<S16>:120' */
  } else {
    /* Transition: '<S16>:119' */
  }

  /* SignalConversion generated from: '<S3>/FlgRonMinDec' incorporates:
   *  DataTypeConversion: '<S13>/Conversion3'
   */
  /* Transition: '<S16>:122' */
  FlgRonMinDec = (uint8_T)(rtb_FlgRonDetect_c ? ((uint8_T)1) : ((uint8_T)0));

  /* Logic: '<S13>/Logical Operator'
   *
   * Block requirements for '<S13>/Logical Operator':
   *  1. EISB_FCA6CYL_SW_REQ_2830: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12244)
   */
  rtb_FlgRonDetect_c = (((rtb_RelationalOperator || rtb_RelationalOperator1) ||
    rtb_FlgRonDetect_c) || rtb_RelationalOperator5);

  /* DataTypeConversion: '<S13>/Conversion1' */
  FlgRonDetect = (uint8_T)(rtb_FlgRonDetect_c ? ((uint8_T)1) : ((uint8_T)0));

  /* SignalConversion generated from: '<S3>/FlgRonMaxInc' incorporates:
   *  DataTypeConversion: '<S13>/Conversion2'
   */
  FlgRonMaxInc = (uint8_T)(rtb_RelationalOperator5 ? ((uint8_T)1) : ((uint8_T)0));

  /* SignalConversion generated from: '<S3>/DRonLevel' incorporates:
   *  Constant: '<S14>/TBDRONLEVEL'
   *  Selector: '<S14>/Selector4'
   *
   * Block requirements for '<S14>/TBDRONLEVEL':
   *  1. EISB_FCA6CYL_SW_REQ_2822: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12236)
   */
  DRonLevel = TBDRONLEVEL[(u1_tmp)];

  /* MinMax: '<S15>/MinMax1' incorporates:
   *  Inport: '<Root>/SecondsWorkTime'
   *  SignalConversion: '<S15>/SignalCopy'
   *
   * Block requirements for '<S15>/SignalCopy':
   *  1. EISB_FCA6CYL_SW_REQ_2823: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12237)
   */
  SecStartRun = SecondsWorkTime;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_Fast' */
}

/* Model step function */
void RonDetectEst_MonitoringInit(void)
{
  int32_T i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_MonitoringInit' incorporates:
   *  SubSystem: '<Root>/MonitoringInit'
   *
   * Block description for '<Root>/MonitoringInit':
   *  This block makes available a function call that can be used to reset
   *  "monitoring" function's signals (usually called by software component
   *  RonDetectMgm).
   *
   * Block requirements for '<Root>/MonitoringInit':
   *  1. EISB_FCA6CYL_SW_REQ_2815: Software shall provide a reset functionality named RonDetectEst_Mo... (ECU_SW_Requirements#12229)
   */
  for (i = 0; i < 20; i++) {
    /* SignalConversion generated from: '<S4>/VtRonLevel' */
    VtRonLevel[(i)] = 127U;

    /* SignalConversion generated from: '<S4>/VtDRonLevel' */
    VtDRonLevel[(i)] = MAX_int8_T;

    /* SignalConversion generated from: '<S4>/VtRonLoad' */
    VtRonLoad[(i)] = 0U;

    /* SignalConversion generated from: '<S4>/VtRonRpm' */
    VtRonRpm[(i)] = 0U;

    /* SignalConversion generated from: '<S4>/VtRonSec' */
    VtRonSec[(i)] = 0U;

    /* SignalConversion generated from: '<S4>/VtRonTAir' */
    VtRonTAir[(i)] = 0;

    /* SignalConversion generated from: '<S4>/VtRonTWater' */
    VtRonTWater[(i)] = 0;
  }

  /* SignalConversion generated from: '<S4>/FlgRonDetect' incorporates:
   *  Constant: '<S4>/Constant'
   */
  FlgRonDetect = 0U;

  /* Sum: '<S13>/Add1' incorporates:
   *  Constant: '<S4>/Constant1'
   *  SignalConversion generated from: '<S4>/CntDRon0'
   *
   * Block requirements for '<S13>/Add1':
   *  1. EISB_FCA6CYL_SW_REQ_2825: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12239)
   */
  CntDRon0 = 0U;

  /* Sum: '<S13>/Add4' incorporates:
   *  Constant: '<S4>/Constant2'
   *  SignalConversion generated from: '<S4>/CntRonMaxInc'
   *
   * Block requirements for '<S13>/Add4':
   *  1. EISB_FCA6CYL_SW_REQ_2827: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12241)
   */
  CntRonMaxInc = 0U;

  /* Sum: '<S13>/Add2' incorporates:
   *  Constant: '<S4>/Constant3'
   *  SignalConversion generated from: '<S4>/CntRonMinDec'
   *
   * Block requirements for '<S13>/Add2':
   *  1. EISB_FCA6CYL_SW_REQ_2826: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12240)
   */
  CntRonMinDec = 0U;

  /* Sum: '<S13>/Add' incorporates:
   *  Constant: '<S4>/Constant4'
   *  SignalConversion generated from: '<S4>/CntRonRun'
   *
   * Block requirements for '<S13>/Add':
   *  1. EISB_FCA6CYL_SW_REQ_2824: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12238)
   */
  CntRonRun = 0U;

  /* SignalConversion generated from: '<S4>/FlgRonMaxInc' incorporates:
   *  Constant: '<S4>/Constant8'
   */
  FlgRonMaxInc = 0U;

  /* SignalConversion generated from: '<S4>/FlgRonMinDec' incorporates:
   *  Constant: '<S4>/Constant9'
   */
  FlgRonMinDec = 0U;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_MonitoringInit' */
}

/* Model step function */
void RonDetectEst_PowerOn(void)
{
  int32_T i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_PowerOn' incorporates:
   *  SubSystem: '<Root>/PowerOn'
   *
   * Block description for '<Root>/PowerOn':
   *  This block resets each model's output at power on event.
   *
   * Block requirements for '<Root>/PowerOn':
   *  1. EISB_FCA6CYL_SW_REQ_2813: Software shall initialize, at power-on event any output with the e... (ECU_SW_Requirements#12227)
   */
  /* SignalConversion generated from: '<S5>/VtDRonLevelSuspRun' */
  for (i = 0; i < 30; i++) {
    VtDRonLevelSuspRun[(i)] = MAX_int8_T;
  }

  /* End of SignalConversion generated from: '<S5>/VtDRonLevelSuspRun' */
  for (i = 0; i < 20; i++) {
    /* SignalConversion generated from: '<S5>/VtRonLevel' */
    VtRonLevel[(i)] = 127U;

    /* SignalConversion generated from: '<S5>/VtDRonLevel' */
    VtDRonLevel[(i)] = MAX_int8_T;

    /* SignalConversion generated from: '<S5>/VtRonLoad' */
    VtRonLoad[(i)] = 0U;

    /* SignalConversion generated from: '<S5>/VtRonRpm' */
    VtRonRpm[(i)] = 0U;

    /* SignalConversion generated from: '<S5>/VtRonSec' */
    VtRonSec[(i)] = 0U;

    /* SignalConversion generated from: '<S5>/VtRonTAir' */
    VtRonTAir[(i)] = 0;

    /* SignalConversion generated from: '<S5>/VtRonTWater' */
    VtRonTWater[(i)] = 0;
  }

  /* SignalConversion generated from: '<S5>/VtDRonLevelSusp' */
  for (i = 0; i < 10; i++) {
    VtDRonLevelSusp[(i)] = MAX_int8_T;
  }

  /* End of SignalConversion generated from: '<S5>/VtDRonLevelSusp' */

  /* SignalConversion: '<S5>/SignalCopy' incorporates:
   *  SignalConversion generated from: '<S5>/RonLevelEE'
   */
  //test2
  /* RonLevelUsed = RonLevelEE; // Removed - managed by RonDetectFuel to prevent oscillation */

  /* SignalConversion generated from: '<S5>/FlgRonDetect' incorporates:
   *  Constant: '<S5>/Constant'
   */
  FlgRonDetect = 0U;

  /* Sum: '<S13>/Add1' incorporates:
   *  Constant: '<S5>/Constant1'
   *  SignalConversion generated from: '<S5>/CntDRon0'
   *
   * Block requirements for '<S13>/Add1':
   *  1. EISB_FCA6CYL_SW_REQ_2825: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12239)
   */
  CntDRon0 = 0U;

  /* Sum: '<S13>/Add4' incorporates:
   *  Constant: '<S5>/Constant2'
   *  SignalConversion generated from: '<S5>/CntRonMaxInc'
   *
   * Block requirements for '<S13>/Add4':
   *  1. EISB_FCA6CYL_SW_REQ_2827: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12241)
   */
  CntRonMaxInc = 0U;

  /* Sum: '<S13>/Add2' incorporates:
   *  Constant: '<S5>/Constant3'
   *  SignalConversion generated from: '<S5>/CntRonMinDec'
   *
   * Block requirements for '<S13>/Add2':
   *  1. EISB_FCA6CYL_SW_REQ_2826: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12240)
   */
  CntRonMinDec = 0U;

  /* Sum: '<S13>/Add' incorporates:
   *  Constant: '<S5>/Constant4'
   *  SignalConversion generated from: '<S5>/CntRonRun'
   *
   * Block requirements for '<S13>/Add':
   *  1. EISB_FCA6CYL_SW_REQ_2824: Software shall provide a function call named RonDetectEst_Fast() t... (ECU_SW_Requirements#12238)
   */
  CntRonRun = 0U;

  /* SignalConversion generated from: '<S5>/CntRonSuspNeg' incorporates:
   *  Constant: '<S5>/Constant5'
   */
  CntRonSuspNeg = 0U;

  /* SignalConversion generated from: '<S5>/CntRonSuspPos' incorporates:
   *  Constant: '<S5>/Constant6'
   */
  CntRonSuspPos = 0U;

  /* SignalConversion generated from: '<S5>/CntRonSuspZero' incorporates:
   *  Constant: '<S5>/Constant7'
   */
  CntRonSuspZero = 0U;

  /* SignalConversion generated from: '<S5>/FlgRonMaxInc' incorporates:
   *  Constant: '<S5>/Constant8'
   */
  FlgRonMaxInc = 0U;

  /* SignalConversion generated from: '<S5>/FlgRonMinDec' incorporates:
   *  Constant: '<S5>/Constant9'
   */
  FlgRonMinDec = 0U;

  /* SignalConversion generated from: '<S5>/DRonLevel' incorporates:
   *  Constant: '<S5>/Constant18'
   */
  DRonLevel = 0;

  /* SignalConversion generated from: '<S5>/IDDRonLevelSusp' incorporates:
   *  Constant: '<S5>/Constant13'
   */
  IDDRonLevelSusp = 0U;

  /* SignalConversion generated from: '<S5>/IDSuspPos' incorporates:
   *  Constant: '<S5>/Constant14'
   */
  IDSuspPos = 0U;

  /* SignalConversion generated from: '<S5>/MaxCntRonSuspNeg' incorporates:
   *  Constant: '<S5>/Constant15'
   */
  MaxCntRonSuspNeg = 0U;

  /* MinMax: '<S15>/MinMax1' incorporates:
   *  Constant: '<S5>/Constant16'
   *  SignalConversion generated from: '<S5>/SecStartRun'
   */
  SecStartRun = 0U;

  /* Constant: '<S5>/Constant10' */
  IdVer_RonDetectEst = ID_VER_RONDETECTEST_DEF;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_PowerOn' */
}

/* Model step function */
void RonDetectEst_Recovery(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_Recovery' incorporates:
   *  SubSystem: '<Root>/Recovery'
   *
   * Block description for '<Root>/Recovery':
   *  This block makes available a function call that can be used to reset
   *  ron level detection in case of recovery (usually called by software
   *  component RonDetectMgm).
   *
   * Block requirements for '<Root>/Recovery':
   *  1. EISB_FCA6CYL_SW_REQ_2819: Software shall provide a function call named RonDetectEst_Recovery... (ECU_SW_Requirements#12233)
   */
  /* SignalConversion generated from: '<S6>/RonLevelEE' incorporates:
   *  Constant: '<S6>/Constant'
   */
  RonLevelEE = RONLEVELREC;
  RonLevelFuel = RonLevelEE;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_Recovery' */
}

/* Model step function */
void RonDetectEst_ResetSecStartRun(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_ResetSecStartRun' incorporates:
   *  SubSystem: '<Root>/ResetSecStartRun'
   *
   * Block description for '<Root>/ResetSecStartRun':
   *  This block makes available a function call that can be used to reset
   *  signal SecStartRun (usually called by software component
   *  RonDetectMgm).
   *
   * Block requirements for '<Root>/ResetSecStartRun':
   *  1. EISB_FCA6CYL_SW_REQ_2818: Software shall provide a function call named RonDetectEst_ResetSec... (ECU_SW_Requirements#12232)
   */
  /* MinMax: '<S15>/MinMax1' incorporates:
   *  Inport: '<Root>/SecondsWorkTime'
   *  SignalConversion: '<S7>/SignalCopy'
   */
  SecStartRun = SecondsWorkTime;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_ResetSecStartRun' */
}

/* Model step function */
void RonDetectEst_Slow(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_S16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_S16_o1;
  uint8_T id_VtD;
  uint8_T idx;
  uint8_T rtb_IDDRonLevelSusp_o;
  int16_T rtb_DataTypeConversion4;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  int32_T i;
  int32_T VtDRonLevelSuspRun_tmp;

  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_Slow' incorporates:
   *  SubSystem: '<Root>/SLOW_DETECTION'
   *
   * Block description for '<Root>/SLOW_DETECTION':
   *  This subsystem implements "background function" which is called to
   *  estimate ron level with a slow test, and is called by software
   *  component RonDetectMgm.
   */
  /* DataTypeConversion: '<S24>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/SATableRonArg'
   */
  rtb_DataTypeConversion4 = SATableRonArg;

  /* S-Function (PreLookUpIdSearch_S16): '<S24>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S21>/BKSARON'
   *  Constant: '<S21>/BKSARON_dim'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1,
                        &rtb_PreLookUpIdSearch_S16_o2, rtb_DataTypeConversion4,
                        &BKSARON[0], ((uint8_T)BKSARON_dim));

  /* SignalConversion generated from: '<S8>/IDDRonLevelSusp_old' */
  /* Gateway: SLOW_DETECTION/BackGround_Mode_Chart */
  /* During: SLOW_DETECTION/BackGround_Mode_Chart */
  /* This Stateflow perform the "slow" ron detection test, used to update run level already detected by "fast" ron detection test.

   */
  /* Entry Internal: SLOW_DETECTION/BackGround_Mode_Chart */
  /* Transition: '<S20>:150' */
  /*  Assign old value for better code generation  */
  rtb_IDDRonLevelSusp_o = IDDRonLevelSusp;

  /* Selector: '<S21>/Selector4' incorporates:
   *  Constant: '<S21>/TBDRONLEVEL'
   *  Inport: '<Root>/CntKIntIdxRonArg'
   *
   * Block requirements for '<S21>/TBDRONLEVEL':
   *  1. EISB_FCA6CYL_SW_REQ_2832: Software shall provide a function call named RonDetectEst_Slow() t... (ECU_SW_Requirements#12246)
   */
  /* Transition: '<S20>:12' */
  /* Store ron level variation  */
  VtDRonLevelSuspRun_tmp = (6 * ((int32_T)rtb_PreLookUpIdSearch_S16_o1)) +
    ((int32_T)CntKIntIdxRonArg);

  /* SignalConversion generated from: '<S8>/IDRonSuspRunEE_old' incorporates:
   *  Constant: '<S21>/TBDRONLEVEL'
   *  Selector: '<S21>/Selector4'
   *
   * Block requirements for '<S21>/TBDRONLEVEL':
   *  1. EISB_FCA6CYL_SW_REQ_2832: Software shall provide a function call named RonDetectEst_Slow() t... (ECU_SW_Requirements#12246)
   */
  VtDRonLevelSuspRun[(IDRonSuspRunEE)] = TBDRONLEVEL[(VtDRonLevelSuspRun_tmp)];

  /* SignalConversion generated from: '<S8>/CntRonSuspRunEE_old' */
  /*  stub this output because not used */
  /* Manage EE counter for slow ron detection algorithm  */
  if (CntRonSuspRunEE != ((uint16_T)MAX_UINT16)) {
    /* Transition: '<S20>:6' */
    CntRonSuspRunEE = (uint16_T)((int32_T)(((int32_T)CntRonSuspRunEE) + 1));
  } else {
    /* Transition: '<S20>:4' */
    /* Transition: '<S20>:157' */
    /* Transition: '<S20>:158' */
  }

  /* End of SignalConversion generated from: '<S8>/CntRonSuspRunEE_old' */
  /* Transition: '<S20>:15' */
  /*  Test always executed, counts negative, positive or zero correction in last executed runs  */
  CntRonSuspPos = 0U;
  CntRonSuspNeg = 0U;
  CntRonSuspZero = 0U;
  idx = 0U;

  /* Constant: '<S8>/Constant2' */
  if (CntRonSuspRunEE < ((uint16_T)RONSUSPRUN)) {
    rtb_PreLookUpIdSearch_U16_o1 = CntRonSuspRunEE;
  } else {
    rtb_PreLookUpIdSearch_U16_o1 = (uint16_T)RONSUSPRUN;
  }

  while (idx < ((uint8_T)rtb_PreLookUpIdSearch_U16_o1)) {
    /* SignalConversion generated from: '<S8>/IDRonSuspRunEE_old' */
    /* Transition: '<S20>:44' */
    /* Transition: '<S20>:165' */
    if (IDRonSuspRunEE >= idx) {
      /* Transition: '<S20>:167' */
      /* Transition: '<S20>:169' */
      id_VtD = (uint8_T)(IDRonSuspRunEE - idx);

      /* Transition: '<S20>:172' */
    } else {
      /* Transition: '<S20>:171' */
      id_VtD = (uint8_T)((IDRonSuspRunEE + ((uint8_T)VT_RON_SUSP_SIZE)) - idx);
    }

    /* Transition: '<S20>:290' */
    if (VtDRonLevelSuspRun[(id_VtD)] > 0) {
      /* Transition: '<S20>:292' */
      /* Transition: '<S20>:294'
       * Requirements for Transition: '<S20>:294':
       *  1. EISB_FCA6CYL_SW_REQ_2834: Software shall provide a function call named RonDetectEst_Slow() t... (ECU_SW_Requirements#12248)
       */
      CntRonSuspPos = (uint8_T)((int32_T)(((int32_T)CntRonSuspPos) + 1));

      /* Transition: '<S20>:301' */
      /* Transition: '<S20>:304' */
    } else {
      /* Transition: '<S20>:296' */
      if (VtDRonLevelSuspRun[(id_VtD)] < 0) {
        /* Transition: '<S20>:298' */
        /* Transition: '<S20>:300'
         * Requirements for Transition: '<S20>:300':
         *  1. EISB_FCA6CYL_SW_REQ_2833: Software shall provide a function call named RonDetectEst_Slow() t... (ECU_SW_Requirements#12247)
         */
        CntRonSuspNeg = (uint8_T)((int32_T)(((int32_T)CntRonSuspNeg) + 1));

        /* Transition: '<S20>:304' */
      } else {
        /* Transition: '<S20>:303'
         * Requirements for Transition: '<S20>:303':
         *  1. EISB_FCA6CYL_SW_REQ_2836: Software shall provide a function call named RonDetectEst_Slow() t... (ECU_SW_Requirements#12249)
         */
        CntRonSuspZero = (uint8_T)((int32_T)(((int32_T)CntRonSuspZero) + 1));
      }
    }

    /* Transition: '<S20>:305' */
    idx = (uint8_T)((int32_T)(((int32_T)idx) + 1));
  }

  /* Chart: '<S8>/BackGround_Mode_Chart' incorporates:
   *  SubSystem: '<S20>/idpos_calc'
   *
   * Block description for '<S20>/idpos_calc':
   *  This block calculates the index on lower and higher thresholds used to
   *  enable ron level decrease or increase.
   */
  /* DataTypeConversion: '<S22>/Conversion' */
  /* Transition: '<S20>:162' */
  /* cntRonSuspPos_loc = CntRonSuspPos; */
  /* Simulink Function 'idpos_calc': '<S20>:311' */
  rtb_PreLookUpIdSearch_U16_o1 = (uint16_T)CntRonSuspPos;

  /* S-Function (PreLookUpIdSearch_U16): '<S23>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S22>/BKRONSUSPPOS'
   *  Constant: '<S22>/BKRONSUSPPOS_dim'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                        &rtb_PreLookUpIdSearch_U16_o2,
                        rtb_PreLookUpIdSearch_U16_o1, &BKRONSUSPPOS[0],
                        ((uint8_T)BKRONSUSPPOS_dim));

  /* DataTypeConversion: '<S22>/Conversion1' */
  IDSuspPos = (uint8_T)rtb_PreLookUpIdSearch_U16_o1;

  /* Constant: '<S8>/Constant2' */
  MaxCntRonSuspNeg = (uint8_T)((RONSUSPRUN - CntRonSuspPos) - CntRonSuspZero);

  /*  Condition to decrease RON level  */
  if ((CntRonSuspNeg >= VTRONSUSPNEGD[(IDSuspPos)]) && (CntRonSuspRunEE >=
       ((uint16_T)RONSUSPRUN))) {
    /* SignalConversion generated from: '<S8>/RonLevelEE_old' incorporates:
     *  SignalConversion generated from: '<S8>/VtDRonLevelSusp'
     *  SignalConversion generated from: '<S8>/CntRonSuspEE_old'
     *  SignalConversion generated from: '<S8>/IDDRonLevelSusp_old'
     *  SignalConversion generated from: '<S8>/IDRonSuspRunEE_old'
     */
    /* Transition: '<S20>:69' */
    /* Transition: '<S20>:178' */
    if (RonLevelEE > ((uint8_T)MIN_RONLEVEL)) {
      /* Transition: '<S20>:83' */
      /* Transition: '<S20>:181'
       * Requirements for Transition: '<S20>:181':
       *  1. EISB_FCA6CYL_SW_REQ_2835: Software shall provide a function call named RonDetectEst_Slow() t... (ECU_SW_Requirements#12250)
       */
      RonLevelEE_FastTestValue = RonLevelEE;
      RonLevelEE = (uint8_T)((int32_T)(((int32_T)RonLevelEE) - 1));
      RonLevelFuel = RonLevelEE;
      VtDRonLevelSusp[(IDDRonLevelSusp)] = -1;
      RonLevelEE_FastTestPoint = 2U;
      //RonLevelEE_FastTestValue = RonLevelEE;

      /* Graphical Function 'RonLevelChanged': '<S20>:233' */
      /* Reset counters when ron level is updated. */
      /* Transition: '<S20>:254' */
      for (i = 0; i < 30; i++) {
        VtDRonLevelSuspRun[(i)] = MAX_int8_T;
      }

      idx = 0U;
      CntRonSuspRunEE = 0U;
      i = ((int32_T)IDDRonLevelSusp) + 1;
      rtb_IDDRonLevelSusp_o = (uint8_T)i;
      if (i >= ((int32_T)((uint8_T)VT_RON_SUSP_TEST_SIZE))) {
        /* Transition: '<S20>:247' */
        /* Transition: '<S20>:244' */
        rtb_IDDRonLevelSusp_o = 0U;

        /* Transition: '<S20>:245' */
      } else {
        /* Transition: '<S20>:252' */
      }

      /* Transition: '<S20>:250' */
      if (CntRonSuspEE != ((uint16_T)MAX_UINT16)) {
        /* Transition: '<S20>:248' */
        CntRonSuspEE = (uint16_T)((int32_T)(((int32_T)CntRonSuspEE) + 1));
      } else {
        /* Transition: '<S20>:253' */
        /* Transition: '<S20>:238' */
        /* Transition: '<S20>:246' */
      }

      /* Transition: '<S20>:251' */
      /* Transition: '<S20>:272' */
    } else {
      /* Transition: '<S20>:218' */
      /* Graphical Function 'RonLevelHold': '<S20>:256' */
      /* Manage EE counter for test that not update ron level */
      /* Transition: '<S20>:265' */
      idx = (uint8_T)((int32_T)(((int32_T)IDRonSuspRunEE) + 1));
      if ((((int32_T)IDRonSuspRunEE) + 1) >= ((int32_T)((uint8_T)
            VT_RON_SUSP_SIZE))) {
        /* Transition: '<S20>:259' */
        idx = 0U;
      } else {
        /* Transition: '<S20>:261' */
        /* Transition: '<S20>:262' */
        /* Transition: '<S20>:260' */
      }

      /* Transition: '<S20>:264' */
    }

    /* Transition: '<S20>:273' */
    /* Transition: '<S20>:274' */
    /* Transition: '<S20>:275' */
  } else {
    /* Transition: '<S20>:199' */
    /*  Condition to increase RON level  */
    if ((MaxCntRonSuspNeg <= VTRONSUSPNEGI[(IDSuspPos)]) && (((int32_T)
          VTRONSUSPNEGI[(IDSuspPos)]) > 0)) {
      /* SignalConversion generated from: '<S8>/RonLevelEE_old' incorporates:
       *  Constant: '<S8>/CNTRONDETECT2'
       *  SignalConversion generated from: '<S8>/VtDRonLevelSusp'
       *  SignalConversion generated from: '<S8>/CntRonSuspEE_old'
       *  SignalConversion generated from: '<S8>/IDDRonLevelSusp_old'
       *  SignalConversion generated from: '<S8>/IDRonSuspRunEE_old'
       */
      /* Transition: '<S20>:202' */
      /* Transition: '<S20>:204' */
      if (RonLevelEE < MAX_RONLEVEL) {
        /* Transition: '<S20>:206' */
        /* Transition: '<S20>:208'
         * Requirements for Transition: '<S20>:208':
         *  1. EISB_FCA6CYL_SW_REQ_2837: Software shall provide a function call named RonDetectEst_Slow() t... (ECU_SW_Requirements#12251)
         */
        RonLevelEE_FastTestValue = RonLevelEE;
        RonLevelEE = (uint8_T)((int32_T)(((int32_T)RonLevelEE) + 1));
        RonLevelFuel = RonLevelEE;
        VtDRonLevelSusp[(IDDRonLevelSusp)] = 1;
        RonLevelEE_FastTestPoint = 1U;
        
        

        /* Graphical Function 'RonLevelChanged': '<S20>:233' */
        /* Reset counters when ron level is updated. */
        /* Transition: '<S20>:254' */
        for (i = 0; i < 30; i++) {
          VtDRonLevelSuspRun[(i)] = MAX_int8_T;
        }

        idx = 0U;
        CntRonSuspRunEE = 0U;
        rtb_IDDRonLevelSusp_o = (uint8_T)((int32_T)(((int32_T)IDDRonLevelSusp) +
          1));
        if ((((int32_T)IDDRonLevelSusp) + 1) >= ((int32_T)((uint8_T)
              VT_RON_SUSP_TEST_SIZE))) {
          /* Transition: '<S20>:247' */
          /* Transition: '<S20>:244' */
          rtb_IDDRonLevelSusp_o = 0U;

          /* Transition: '<S20>:245' */
        } else {
          /* Transition: '<S20>:252' */
        }

        /* Transition: '<S20>:250' */
        if (CntRonSuspEE != ((uint16_T)MAX_UINT16)) {
          /* Transition: '<S20>:248' */
          CntRonSuspEE = (uint16_T)((int32_T)(((int32_T)CntRonSuspEE) + 1));
        } else {
          /* Transition: '<S20>:253' */
          /* Transition: '<S20>:238' */
          /* Transition: '<S20>:246' */
        }

        /* Transition: '<S20>:251' */
        /* Transition: '<S20>:274' */
        /* Transition: '<S20>:275' */
      } else {
        /* Transition: '<S20>:215' */
        /* Graphical Function 'RonLevelHold': '<S20>:256' */
        /* Manage EE counter for test that not update ron level */
        /* Transition: '<S20>:265' */
        idx = (uint8_T)((int32_T)(((int32_T)IDRonSuspRunEE) + 1));
        if ((((int32_T)IDRonSuspRunEE) + 1) >= ((int32_T)((uint8_T)
              VT_RON_SUSP_SIZE))) {
          /* Transition: '<S20>:259' */
          idx = 0U;
        } else {
          /* Transition: '<S20>:261' */
          /* Transition: '<S20>:262' */
          /* Transition: '<S20>:260' */
        }

        /* Transition: '<S20>:264' */
        /* Transition: '<S20>:275' */
      }
    } else {
      /* SignalConversion generated from: '<S8>/IDRonSuspRunEE_old' */
      /* Transition: '<S20>:213' */
      /* Graphical Function 'RonLevelHold': '<S20>:256' */
      /* Manage EE counter for test that not update ron level */
      /* Transition: '<S20>:265' */
      i = ((int32_T)IDRonSuspRunEE) + 1;
      idx = (uint8_T)i;
      if (i >= ((int32_T)((uint8_T)VT_RON_SUSP_SIZE))) {
        /* Transition: '<S20>:259' */
        idx = 0U;
      } else {
        /* Transition: '<S20>:261' */
        /* Transition: '<S20>:262' */
        /* Transition: '<S20>:260' */
      }

      /* Transition: '<S20>:264' */
    }
  }

  /* Constant: '<S8>/Constant1' */
  /* Transition: '<S20>:279' */
  if (FORCEDRONLEVEL <= ((uint8_T)MAX_RONLEVEL_EXP)) {
    /* Transition: '<S20>:283' */
    /* Transition: '<S20>:285' */
    RonLevelEE = FORCEDRONLEVEL;

    /* Transition: '<S20>:276' */
  } else {
    /* Transition: '<S20>:280' */
  }

  /* End of Constant: '<S8>/Constant1' */

  /* SignalConversion generated from: '<S8>/FlgRonDetect' */
  /* Transition: '<S20>:286' */
  FlgRonDetect = 0U;

  /* SignalConversion generated from: '<S8>/IDDRonLevelSusp' */
  IDDRonLevelSusp = rtb_IDDRonLevelSusp_o;

  /* SignalConversion generated from: '<S8>/IDRonSuspRunEE' */
  IDRonSuspRunEE = idx;

  /* SignalConversion generated from: '<S8>/DRonLevel' incorporates:
   *  Constant: '<S21>/TBDRONLEVEL'
   *  Selector: '<S21>/Selector4'
   *
   * Block requirements for '<S21>/TBDRONLEVEL':
   *  1. EISB_FCA6CYL_SW_REQ_2832: Software shall provide a function call named RonDetectEst_Slow() t... (ECU_SW_Requirements#12246)
   */
  DRonLevel = TBDRONLEVEL[(VtDRonLevelSuspRun_tmp)];

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEst_Slow' */
}

/* Model initialize function */
void RonDetectEst_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint8_T RonLevelEE;
/* RonLevelUsed removed - defined in RonDetectFuel.c to prevent oscillation */
uint8_T FlgRonDetect;
uint8_T CntDRon0;
uint8_T CntRonMaxInc;
uint8_T CntRonMinDec;
uint8_T CntRonRun;
uint8_T CntRonSuspNeg;
uint8_T CntRonSuspPos;
uint8_T CntRonSuspZero;
uint8_T FlgRonMaxInc;
uint8_T FlgRonMinDec;
uint16_T CntRonSuspRunEE;
uint8_T IDRonSuspRunEE;
uint16_T CntRonSuspEE;
int8_T VtDRonLevelSusp[10];
int8_T VtDRonLevelSuspRun[30];
uint8_T VtRonLevel[20];
void RonDetectEst_Stub(void)
{
  RonLevelEE = 0u;
  /* RonLevelUsed initialization removed - managed by RonDetectFuel.c */
  FlgRonDetect = 0u;
  CntDRon0 = 0u;
  CntRonMaxInc = 0u;
  CntRonMinDec = 0u;
  CntRonRun = 0u;
  CntRonSuspNeg = 0u;
  CntRonSuspPos = 0u;
  CntRonSuspZero = 0u;
  FlgRonMaxInc = 0u;
  FlgRonMinDec = 0u;
  CntRonSuspRunEE = 0u;
  IDRonSuspRunEE = 0u;
  CntRonSuspEE = 0u;
  uint8_T idx;
  for (idx=0;idx<10;idx++) {
    VtDRonLevelSusp[idx] = 0;
  }

  for (idx=0;idx<30;idx++) {
    VtDRonLevelSuspRun[idx] = 0;
  }

  for (idx=0;idx<20;idx++) {
    VtRonLevel[idx] = 0u;
  }
}

void RonDetectEst_PowerOn(void)
{
  RonDetectEst_Stub();
}

void RonDetectEst_100ms(void)
{
  RonDetectEst_Stub();
}

void RonDetectEst_5ms(void)
{
  RonDetectEst_Stub();
}

void RonDetectEst_Fast(void)
{
  RonDetectEst_Stub();
}

void RonDetectEst_Slow(void)
{
  RonDetectEst_Stub();
}

void RonDetectEst_ResetSecStartRun(void)
{
  RonDetectEst_Stub();
}

void RonDetectEst_Recovery(void)
{
  RonDetectEst_Stub();
}

void RonDetectEst_BackgroundInit(void)
{
  RonDetectEst_Stub();
}

void RonDetectEst_MonitoringInit(void)
{
  RonDetectEst_Stub();
}

void RonDetectEst_EOA(void)
{
  RonDetectEst_Stub();
}

#endif                                 /* _BUILD_RONDETECTEST*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/
