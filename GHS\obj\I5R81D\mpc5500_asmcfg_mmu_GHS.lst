
                                   Fri Jul 04 14:35:16 2025           Page 1
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
Command Line:   C:\ghs\comp_201516\asppc.exe -noundefined -elf -b1
                -I..\tree\BIOS\GTM\include -I..\tree\BIOS\GTM\cfg
                -I..\tree\COMMON\CONFIG\asm -I..\tree\COMMON\CONFIG\C
                -I..\tree\COMMON\INCLUDE -I..\tree\COMMON\LIB
                -I..\tree\BIOS\COMMON -I..\tree\AK_OSEK -I..\tree\DD\COMMON
                -I..\tree\APPLICATION\COMMON -I..\tree\EEPCOM -I..\common
                -IC:\ghs\comp_201516\lib\ppc5744 -cpu=ppc5744kz410 -noSPE
                -dbo=C:\Users\<USER>\Desktop\EISB8C_SI_03_RonDetectFuel\GHS\obj\I5R81D\mpc5500_asmcfg_mmu_GHS.dbo
                --gh_oname=obj\I5R81D\mpc5500_asmcfg_mmu_GHS.o
                --gh_md=obj\I5R81D\mpc5500_asmcfg_mmu_GHS.d -asm3g
                -asm3g_driver=C:\ghs\comp_201516\ccppc
                -asm3g_args=@@obj\I5R81D\mpc5500_asmcfg_mmu_GHS.a3g
                -accept_unsafe_op_names -o obj\I5R81D\mpc5500_asmcfg_mmu_GHS.o
                -list=obj\I5R81D\mpc5500_asmcfg_mmu_GHS.lst
                ..\tree\BIOS\STARTUP\mpc5500_asmcfg_mmu_GHS.s
Source File:    ..\tree\BIOS\STARTUP\mpc5500_asmcfg_mmu_GHS.s
Directory:      C:\Users\<USER>\Desktop\EISB8C_SI_03_RonDetectFuel\GHS
Host OS:        Windows
AS: Copyright (C) 1983-2015 Green Hills Software.  All Rights Reserved.
Release: Compiler v2015.1.6
Build Directory: [Directory] COMP-VAL-WIN31:l:/compiler/release-branch-2015-1-comp/build/v2015.1-2015-10-18/win32-comp-ecom
Revision: [VCInfo] http://toolsvc/branches/release-branch-70/src@543895 (built by auto-compiler)
Revision Date: Mon Oct 19 08:19:32 2015

Release Date: Mon Oct 19 11:04:55 2015

                             1	#**************************************************************************/
                             2	#* FILE NAME: mpc5500_asmcfg.s              COPYRIGHT (c) Freescale 2004  */
                             3	#*                                                All Rights Reserved     */
                             4	#* DESCRIPTION:                                                           */
                             5	#* This file contains functions for the MPC5500 assembly configuration.   */
                             6	#=========================================================================*/
                             7	#*                                                                        */
                             8	#* REV      AUTHOR       DATE       DESCRIPTION OF CHANGE                 */
                             9	#* ---   -----------   ----------   ---------------------                 */
                            10	#* 0.1   G. Jackson    26/Mar/04    Initial version                       */ 
                            11	#* 0.2   G. Jackson    29/Apr/04    Made compiler names unique for        */
                            12	#*                                    assembly configuration.             */
                            13	#*                                  Single designation for rcw values.    */
                            14	#* 0.3   G. Jackson    13/May/04    Changed definition of FMPLL_SYNCR     */
                            15	#*                                    register settings.                  */
                            16	#* 0.4   G. Jackson    15/May/04    Removed msync and isync from tlbwe    */
                            17	#*                                    commands.                           */
                            18	#* 0.5   G. Jackson    25/May/04    Changed __OPCOUNT to __SRAM_LOAD_SIZE */
                            19	#*                                  Changed __SRAM_OPCODE to __SRAM_LOAD  */
                            20	#*                                  Changed cfg_OPCODE to cfg_SRAM_LOAD   */
                            21	#*                                  Changed OPCNT_OFFSET to IP_ADVANCE    */
                            22	#* 0.6   G. Jackson    12/Jun/04    Changed TLB entries to work with      */
                            23	#*                                  MPC5554 Rev. 0.3 and later for the    */
                            24	#*                                  BAM, PBRIDGE_B, and Internal FLASH.   */
                            25	#* 0.7   G. Jackson    30/Jun/04    Added entries for RCHW (RCHW_VAL)     */
                            26	#* 0.8   G. Jackson    05/Aug/04    Added cfg_PNTRS for R13 and R2        */
                            27	#* 0.9   G. Jackson    18/Aug/04    Added cfg_ROMCPY for .data and .sdata */
                            28	#* 0.91  G. Jackson    20/Sep/04    cfg_ROMCPY changed to load by bytes.  */
                            29	#* 0.92  G. Jackson    11/Oct/04    L1CSR0 checks added for complete      */
                            30	#*                                    cache operation.                    */
                            31	#* 1.0   G. Jackson    12/Oct/04    Green Hills now does not require      */
                            32	#*                                    quotation marks around the section  */
                            33	#*                                  Added syntax to generate symbols for  */
                            34	#*                                    debug.                              */
                            35	#**************************************************************************/
                            36	
                            37	    
                            38	   // #include "asm_ghs_abstraction.h", MC used explicit vle instructions

                                   Fri Jul 04 14:35:16 2025           Page 2
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                            39	
                            40	    .equ __GRNHS__,  1  // Designation for the Green Hills compiler
                            41	    .equ __PEGNU__,  0  // Designation for the P&E Micro Gnu compiler
                            42	    .equ __DIABCC__, 0  // Designation for the Wind River compiler
                            43	    .equ __CWWRKS__, 0  // Designation for the Metrowerks CodeWarrior compiler
                            44	    
                             0*	    .include "../../tree/COMMON/CONFIG/ASM/mpc5500_usrdefs.inc"
                             1*	#************************************************************************
                             2*	#* FILE NAME: mpc5500_usrdefs.inc            COPYRIGHT (c) Freescale 2004 
                             3*	#*                                                All Rights Reserved     
                             4*	#* DESCRIPTION:                                                           
                             5*	#* This file contains user definitions for the MPC5500 assembly functions.
                             6*	#* The user will only need to make changes to this file for the assembly
                             7*	#*  portion of this code.
                             8*	#* 
                             9*	#*========================================================================
                            10*	#* ORIGINAL AUTHOR: G. Jackson           
                            11*	#* REV      AUTHOR        DATE       DESCRIPTION OF CHANGE                
                            12*	#* ---   -----------   -----------   ---------------------                  
                            13*	#* 0.1   G. Jackson     12/Apr/04    Initial version        
                            14*	#* 0.2   G. Jackson     15/Apr/04    Added compiler designations
                            15*	#* 0.3   G. Jackson     13/May/04    Added runtime variables
                            16*	#* 0.4   G. Jackson     06/Jun/04    Added EXT_BOOT config option
                            17*	#* 0.5   G. Jackson     30/Jun/04    Added RCHW variables
                            18*	#* 1.0   G. Jackson     07/Oct/04    Internal and external RAM set to 
                            19*	#*                                    CACHE_INHIBIT (TLBs 3 & 11)
                            20*	#************************************************************************
                             0*	    .include "../tree/COMMON/CONFIG/ASM/mpc5500_defs.inc"
                             1*	#*************************************************************************
                             2*	#* FILE NAME: mpc5500_defs.inc              COPYRIGHT (c) Freescale 2004 
                             3*	#*                                                All Rights Reserved     
                             4*	#* DESCRIPTION:                                                           
                             5*	#* This file contains prototypes and definitions for the MPC5500 
                             6*	#*  assembly functions.
                             7*	#*
                             8*	#*  Users should make no changes in this file. 
                             9*	#*  User defines are in mpc5500_usrdefs.inc
                            10*	#*========================================================================
                            11*	#* ORIGINAL AUTHOR: G. Jackson           
                            12*	#* REV      AUTHOR        DATE       DESCRIPTION OF CHANGE                
                            13*	#* ---   -----------   -----------   ---------------------                  
                            14*	#* 0.1   G. Jackson     26/Mar/04    Set up assembly definitions     
                            15*	#* 0.2   G. Jackson     15/Apr/04    Add SRAM address definitions    
                            16*	#* 0.3   G. Jackson     13/May/04    Removed 20MSB address definitions
                            17*	#* 1.0   G. Jackson     25/May/04    Changed OPCNT_OFFSET to IP_ADVANCE
                            18*	#* 1.1   R. Dees        25/May/04    Added more comments
                            19*	#* 1.2   G. Jackson     30/Jun/04    Added RCHW constants
                            20*	#*************************************************************************
                            21*	
                            22*	#*************************************************************
                            23*	#*************************************************************
                            24*	# User should not modify any of the definitions below
                            25*	
                            26*	#************************************************************************
                            27*	#                            Definitions                                 
                            28*	#************************************************************************

                                   Fri Jul 04 14:35:16 2025           Page 3
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                            29*	
                            30*	# Base addresses
                            31*	    .equ FLASH_BASE_ADDR,     0x00000000
                            32*	    .equ SRAM_BASE_ADDR,      0x40000000            ## INT_SRAM_BASE
                            33*	#   .equ INT_SRAM_SIZE,       0x0000C000            ## MPC5632M
                            34*	#   .equ INT_SRAM_SIZE,       0x00010000            ## MPC5633M
                            35*	    .equ INT_SRAM_SIZE,       0x00017800            ## MPC5634M
                            36*	    .equ INT_SRAM_128BYTSEGS, (INT_SRAM_SIZE >> 7)  ## Number of 128 byte segments
                            37*	    .equ SHDW_BLK_ADDR,       0x00FFC000            ## Shadow Block starts at 0xFF_FC00 for 1K size
                            38*	    .equ BAM_BASE_ADDR,       0x11300000
                            39*	    .equ EXTMEM1_BASE_ADDR,   0x20000000
                            40*	    .equ EXTMEM2_BASE_ADDR,   0x3FF80000
                            41*	
                            42*	    .equ PBRIDGEA_BASE_ADDR,  0xC3F00000
                            43*	    .equ EXTBUSINT_BASE_ADDR, 0x20000000
                            44*	    .equ SIU_BASE_ADDR,       0xC3F90000
                            45*	    .equ ETPU_BASE_ADDR,      0xC3FC0000
                            46*	
                            47*	    .equ PBRIDGEB_BASE_ADDR,  0xFFF00000
                            48*	    .equ XBAR_BASE_ADDR,      0xFFF04000
                            49*	    .equ ECSM_BASE_ADDR,      0xFFF40000
                            50*	    .equ EDMA_BASE_ADDR,      0xFFF44000
                            51*	    .equ INTC_BASE_ADDR,      0xFFF48000
                            52*	    .equ EQADC_BASE_ADDR,     0xFFF80000
                            53*	
                            54*	    .equ EXTBUSINT_PHY_ADDR,  0x00000000
                            55*	
                            56*	# Size for defined address spaces
                            57*	    .equ SIZE_4M,    0x00400000  #   4 MB
                            58*	    .equ SIZE_1M,    0x00100000  #   1 MB
                            59*	    .equ SIZE_768K,  0x000C0000  # 768 KB 
                            60*	    .equ SIZE_512K,  0x00080000  # 512 KB 
                            61*	    .equ SIZE_256K,  0x00040000  # 256 KB 
                            62*	    .equ SIZE_64K,   0x00010000  #  64 KB 
                            63*	    .equ SIZE_48K,   0x0000C000  #  48 KB 
                            64*	    .equ SIZE_4K,    0x00001000  #   4 KB
                            65*	
                            66*	#*************** Reset Configuration Half Word constants *********
                            67*	    .equ WDOG_DISABLE, 0x00000000  # Watchdog is default disabled
                            68*	    .equ WDOG_ENABLE,  0x04000000  # Watchdog is write once enabled
                            69*	    .equ CS0_32BIT,    0x00000000  # CS0 external data bus is 32-bits
                            70*	    .equ CS0_16BIT,    0x02000000  # CS0 external data bus is 16-bits
                            71*	    .equ MPC5500_ID,   0x005A0000  # RCHW boot ID for MPC5500 devices
                            72*	
                            73*	#*************** Cache initialization constants **************
                            74*	# Definitions for the L1CSR1 (SPR1011) (L1 Cache control and Status Register 1)
                            75*	# bit reference: L1CSR1 ((ICECE<<16)|(ICEI<<15)|(ICLOC<<13)|(ICEA<<5)|(ICLOINV<<4)|(ICABT<<2)|(ICINV<<1)|(ICE)) 
                            76*	# Fields used for L1CSR1 (L1 Cache Control and Status Register 1)
                            77*	# ICE = Instruction Cache Enable = bit [31]
                            78*	    .equ ICE_ENABLE,   0x00000001  # 0x1
                            79*	    .equ ICE_DISABLE,  0x00000000  # 0x0
                            80*	# ICINV = Instruction Cache Invalidate = bit [30]
                            81*	    .equ ICINV_INV_OP, 0x00000002  # 0x1
                            82*	    .equ ICINV_NO_OP,  0x00000000  # 0x0
                            83*	# ICABT = Instruction Cache Operation Aborted = bit [29]
                            84*	    .equ ICABT_ABORT_OP,       0x00000004  # 0x1

                                   Fri Jul 04 14:35:16 2025           Page 4
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                            85*	    .equ ICINV_CLEANABORT_OP,  0x00000000  # 0x0
                            86*	# ICLOINV = Instruction Cache Lockout Indicator Invalidate = bit [27]
                            87*	    .equ ICLOINV_INV_OP, 0x00000010  # 0x1
                            88*	    .equ ICLOINV_NO_OP,  0x00000000  # 0x0
                            89*	# ICEA = Instruction Cache Error Action = bit [25-26]
                            90*	    .equ ICHECKERR_EXCEN,  0x00000000  # 0x0
                            91*	    .equ ICHECKERR_AUTCOR, 0x00000020  # 0x1
                            92*	# ICLOC = Instruction Cache Lockout Control = bit [17:18]
                            93*	    .equ ICLOC_DISABLE, 0x00000000           # 0x0
                            94*	    .equ ICLOC_ENABLE_NOEXC,  0x00002000     # 0x1
                            95*	    .equ ICLOC_ENABLE_EXCEN,  0x00004000     # 0x2
                            96*	    .equ ICLOC_ENABLE_EXCENTAG,  0x00006000  # 0x3
                            97*	# ICEI = Instruction Cache Error Injection Enable = bit [16], for test purpose
                            98*	    .equ ICERR_INJDIS,  0x00000000  # 0x0
                            99*	    .equ ICERR_INJEN,   0x00008000  # 0x1
                           100*	# ICECE = Cache Error Checking Enable = bit [15]
                           101*	    .equ ICHECKERR_DISABLE,  0x00000000  # 0x0
                           102*	    .equ ICHECKERR_ENABLE,   0x00010000  # 0x1
                           103*	
                           104*	#*************** FMPLL initialization constants **************
                           105*	# Define the address for the FMPLL registers 
                           106*	    .equ FMPLL_SYNCRREG, 0xC3F80000
                           107*	    .equ FMPLL_SYNSRREG, 0xC3F80004
                           108*	    .equ SIU_ECCRREG,    0xC3F90984
                           109*	    .equ SIU_SRCRREG,    0xC3F90010
                           110*	
                           111*	#*************************************************************
                           112*	# Definitions for FMPLL_SYNCR (FMPLL Synthesizer Control Register) 
                           113*	# bit reference: SYNCR((MFD<<23)|(RFD<<19)|(locen<<18)|
                           114*	#                 (lolre<<17)|(locre<<16)|(disclk<<15)|(lolirq<<14)|
                           115*	#                 (locirq<<13)|(rate<<12)|(depth<<10)|(exp))
                           116*	
                           117*	# Fields used for PREDIV (Pre-Divider, bits [1:3]) 
                           118*	    .equ PREDIV_1,  0x00000000  # 0x0
                           119*	    .equ PREDIV_2,  0x10000000  # 0x1
                           120*	    .equ PREDIV_3,  0x20000000  # 0x2
                           121*	    .equ PREDIV_4,  0x30000000  # 0x3
                           122*	    .equ PREDIV_5,  0x40000000  # 0x4
                           123*	    .equ PREDIV_6,   0x50000000  # 0x5
                           124*	    .equ PREDIV_7,   0x60000000  # 0x6
                           125*	    .equ PREDIV_INH, 0x70000000  # 0x7 clock inhibit
                           126*	
                           127*	# Fields used for MFD (Muliplication Factor Divider, bits [4:8]) 
                           128*	    .equ MFD_8,  0x02000000  # 0x4
                           129*	    .equ MFD_9,  0x02800000  # 0x5
                           130*	    .equ MFD_10, 0x03000000  # 0x6
                           131*	    .equ MFD_11, 0x03800000  # 0x7
                           132*	    .equ MFD_12, 0x04000000  # 0x8
                           133*	    .equ MFD_13, 0x04800000  # 0x9
                           134*	    .equ MFD_14, 0x05000000  # 0xA
                           135*	    .equ MFD_15, 0x05800000  # 0xB
                           136*	    .equ MFD_16, 0x06000000  # 0xC
                           137*	    .equ MFD_17, 0x06800000  # 0xD
                           138*	    .equ MFD_18, 0x07000000  # 0xE
                           139*	    .equ MFD_19, 0x07800000  # 0xF
                           140*	    .equ MFD_20, 0x08000000  # 0x10

                                   Fri Jul 04 14:35:16 2025           Page 5
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                           141*	    .equ MFD_21, 0x08800000  # 0x11
                           142*	    .equ MFD_22, 0x09000000  # 0x12
                           143*	    .equ MFD_23, 0x09800000  # 0x13
                           144*	    .equ MFD_24, 0x0a000000  # 0x14
                           145*	
                           146*	# Fields used for RFD (Reduced Frequency Divider, bits [10:12]) 
                           147*	    .equ RFD_1,   0x00000000  # 0x0
                           148*	    .equ RFD_2,   0x00080000  # 0x1
                           149*	    .equ RFD_4,   0x00100000  # 0x2
                           150*	
                           151*	# Fields for LOCEN (Loss-of-clock enable, bit [13]) 
                           152*	    .equ LOCEN_DIS, 0x00000000  # 0x0
                           153*	    .equ LOCEN_EN,  0x00040000  # 0x1
                           154*	
                           155*	# Fields for LOLRE (Loss-of-lock reset enable, bit [14]) 
                           156*	    .equ LOLRE_IGNORE,   0x00000000  # 0x0
                           157*	    .equ LOLRE_ASSERT,   0x00020000  # 0x1
                           158*	
                           159*	# Fields for LOCRE (Loss-of-clock reset enable, bit [15]) 
                           160*	    .equ LOCRE_IGNORE,   0x00000000  # 0x0
                           161*	    .equ LOCRE_ASSERT,   0x00010000  # 0x1
                           162*	
                           163*	# Fields for DISCLK (Disable CLKOUT, bit [16]) 
                           164*	    .equ DISCLK_NORMAL, 0x00000000  # 0x0
                           165*	    .equ DISCLK_LOW,    0x00008000  # 0x1
                           166*	
                           167*	# Fields for LOLIRQ (Loss-of-lock interrupt request, bit [17]) 
                           168*	    .equ LOLIRQ_NOT_REQUESTED, 0x00000000  # 0x0
                           169*	    .equ LOLIRQ_REQUESTED,     0x00004000  # 0x1
                           170*	
                           171*	# Fields for LOCIRQ (Loss-of-clock interrupt request, bit [18]) 
                           172*	    .equ LOCIRQ_NOT_REQUESTED, 0x00000000  # 0x0
                           173*	    .equ LOCIRQ_REQUESTED,     0x00002000  # 0x1
                           174*	
                           175*	# Fields for RATE (Modulation rate, bit [19]) 
                           176*	    .equ RATE_FREF_80, 0x00000000  # 0x0
                           177*	    .equ RATE_FREF_40, 0x00001000  # 0x1
                           178*	
                           179*	# Fields for DEPTH (Modulation depth percentage, bits [20:21])
                           180*	    .equ DEPTH_0, 0x00000000  # 0x0
                           181*	    .equ DEPTH_1, 0x00000400  # 0x1
                           182*	    .equ DEPTH_2, 0x00000800  # 0x2
                           183*	
                           184*	# Fields for EXP (Expected difference, bits [22:31])
                           185*	    .equ EXP_0, 0x00000000  # 0x0
                           186*	
                           187*	#*************************************************************
                           188*	# Definitions for the FMPLL_SYNSR (Synthesizer Status Register) 
                           189*	# bit reference: SYNSR ((lolf<<9)|(locf<<2))
                           190*	
                           191*	# Fields for LOLF (Loss-of-lock flag, bit [22]) 
                           192*	    .equ LOLF_NO_CHANGE, 0x00000000  # 0x0
                           193*	    .equ LOLF_CLEAR,     0x00000200  # 0x1
                           194*	
                           195*	# Fields for LOCF (Loss-of-clock flag, bit [29]) 
                           196*	    .equ LOCF_NO_CHANGE, 0x00000000  # 0x0

                                   Fri Jul 04 14:35:16 2025           Page 6
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                           197*	    .equ LOCF_CLEAR,     0x00000004  # 0x1
                           198*	
                           199*	#*************************************************************
                           200*	# Definitions for the SIU_ECCR (External Clock Control Register) 
                           201*	# bit reference: ECCR((ENGDIV<<8)|(EBTS<<3)|(EBDF)) 
                           202*	    .equ ECCRREG, 0xC3F90984  # ECCR register address
                           203*	
                           204*	# Fields for ENGDIV (Engineering clock values, bits [18:23]) 
                           205*	    .equ ENGDIV_BY128, 0x00003F00  # 0x3F
                           206*	
                           207*	# Fields for EBTS (external signals hold time, bit [28]) 
                           208*	    .equ EBTS_NO_HOLD, 0x00000000  # 0x0
                           209*	    .equ EBTS_HOLD,    0x00000004  # 0x1
                           210*	
                           211*	# Fields for EBDF (CLKOUT divides, bits [30:31]) 
                           212*	    .equ EBDF_DIVBY2, 0x00000001  # 0x1
                           213*	    .equ EBDF_DIVBY4, 0x00000002  # 0x2
                           214*	
                           215*	#*************************************************************
                           216*	# Definitions for the SIU_SRCR (System Reset Control Register) 
                           217*	# bit reference: SIU_SRCR ((SSR<<31)|(SER<<30)|(CRE<<15))
                           218*	
                           219*	# Fields for SSR (software system reset, bit [0]) 
                           220*	    .equ SSR_NORST,  0x00000000  # 0x0
                           221*	    .equ SSR_SYSRST, 0x80000000  # 0x1
                           222*	
                           223*	# Fields for SER (external system reset, bit [1]) 
                           224*	    .equ SER_NORST, 0x00000000  # 0x0
                           225*	    .equ SER_XRST,  0x40000000  # 0x1
                           226*	
                           227*	# Fields for CRE (checkstop reset enable, bit [16]) 
                           228*	    .equ CRE_NO, 0x00000000  # 0x0
                           229*	    .equ CRE_EN, 0x00008000  # 0x1
                           230*	
                           231*	
                           232*	#*************** Flash initialization constants ****************
                           233*	
                           234*	# Definitions for FLASH_BIUCR (Flash BIU Control Register) 
                           235*	# bit reference: FLSHBIUCR ((M3PFE<<19)|(M2PFE<<18)|
                           236*	#               (M1PFE<<17)|(M0PFE<<16)|(APC<<13)|(WWSC<<11)|
                           237*	#               (RWSC<<8)|(DPFEN<<6)|(IPFEN<<4)|(PFLIM<<1)|(BFEN))
                           238*	
                           239*	# Fields for Flash Bus Interface Control 
                           240*	# Fields for Prefetch Control (MnPFE Master n Prefetch Enable)
                           241*	# Fields for M3PFE (Master 3 (EBI) prefetch enable, bit [12])
                           242*	    .equ EBI_PREFTCH_OFF,  0x00000000  # 0x0
                           243*	    .equ EBI_PREFTCH_ON,   0x00080000  # 0x1
                           244*	# Fields for M2PFE (Master 2 (eDMA) prefetch enable, bit [13])
                           245*	    .equ EDMA_PREFTCH_OFF, 0x00000000  # 0x0
                           246*	    .equ EDMA_PREFTCH_ON,  0x00040000  # 0x1
                           247*	# Fields for M1PFE (Master 1 (Nexus) prefetch enable, bit [14])
                           248*	    .equ NEX_PREFTCH_OFF,  0x00000000  # 0x0
                           249*	    .equ NEX_PREFTCH_ON,   0x00020000  # 0x1
                           250*	# Fields for M0PFE (Master 0 (e200Z6 core) prefetch enable, bit [15])
                           251*	    .equ CPU_PREFTCH_OFF,  0x00000000  # 0x0
                           252*	    .equ CPU_PREFTCH_ON,   0x00010000  # 0x1

                                   Fri Jul 04 14:35:16 2025           Page 7
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                           253*	
                           254*	# Fields for APC (access pipelining control, bits [16:18]) 
                           255*	    .equ APC_1,  0x00002000  # 0x1
                           256*	    .equ APC_2,  0x00004000  # 0x2
                           257*	    .equ APC_3,  0x00006000  # 0x3
                           258*	    .equ APC_4,  0x00008000  # 0x4
                           259*	    .equ APC_5,  0x0000A000  # 0x5
                           260*	    .equ APC_6,  0x0000C000  # 0x6
                           261*	    .equ APC_NO, 0x0000E000  # 0x7
                           262*	
                           263*	# Fields for WWSC (write wait state control, bits [19:20]) 
                           264*	    .equ WWSC_1, 0x00000800  # 0x1
                           265*	    .equ WWSC_2, 0x00001000  # 0x2
                           266*	    .equ WWSC_3, 0x00001800  # 0x3
                           267*	
                           268*	# Fields for RWSC (read wait state control, bits [21:23]) 
                           269*	    .equ RWSC_0, 0x00000000  # 0x0
                           270*	    .equ RWSC_1, 0x00000100  # 0x1
                           271*	    .equ RWSC_2, 0x00000200  # 0x2
                           272*	    .equ RWSC_3, 0x00000300  # 0x3
                           273*	    .equ RWSC_4, 0x00000400  # 0x4
                           274*	    .equ RWSC_5, 0x00000500  # 0x5
                           275*	    .equ RWSC_6, 0x00000600  # 0x6
                           276*	    .equ RWSC_7, 0x00000700  # 0x7
                           277*	
                           278*	# Fields for DPFEN (data prefetch enable, bits [24:25]) 
                           279*	    .equ DPFEN_NO,   0x00000000  # 0x0
                           280*	    .equ DPFEN_BRST, 0x00000040  # 0x1
                           281*	    .equ DPFEN_ANY,  0x000000C0  # 0x3
                           282*	
                           283*	# Fields for IPFEN (instruction prefetch enable, bits [26:27]) 
                           284*	    .equ IPFEN_NO,   0x00000000  # 0x0
                           285*	    .equ IPFEN_BRST, 0x00000010  # 0x1
                           286*	    .equ IPFEN_ANY,  0x00000030  # 0x3
                           287*	
                           288*	# Fields for PFLIM (additional line prefetch (limit), bits [28:30])
                           289*	    .equ PFLIM_0,  0x00000000  # 0x0 
                           290*	    .equ PFLIM_1,  0x00000002  # 0x1
                           291*	    .equ PFLIM_2,  0x00000004  # 0x2
                           292*	    .equ PFLIM_3,  0x00000006  # 0x3
                           293*	    .equ PFLIM_4,  0x00000008  # 0x4
                           294*	    .equ PFLIM_5,  0x0000000A  # 0x5
                           295*	    .equ PFLIM_U,  0x0000000C  # 0x6
                           296*	
                           297*	# Fields for BFEN (enable line read buffer hits, bit [31]) 
                           298*	    .equ BFEN_DIS, 0x00000000  # 0x0
                           299*	    .equ BFEN_EN,  0x00000001  # 0x1
                           300*	
                           301*	# Define the address for the Flash BIUCR register
                           302*	    .equ FLASH_BIUCRREG, 0xC3F8801C 
                           303*	
                           304*	# set up for errata 105 flash access by all masters.
                           305*	    .equ FLASHACCESS,  0xFFFF
                           306*	    .equ FLASH_BIUAPR, 0xC3F88020
                           307*	
                           308*	#*************************************************************

                                   Fri Jul 04 14:35:16 2025           Page 8
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                           309*	# Definitions for CS0OR0 (Chip Select 0 option register) 
                           310*	# bit reference: CS0OR0AVAL ((AM<<12)|(SCY<<4)|(BSCY<<1))
                           311*	
                           312*	# Fields for CS0OR0
                           313*	# Fields for AM (Address Mask, bits [0:16] ([0:2]=b111))
                           314*	    .equ AMASK_8M,  0xFF800000 # 0xFF800; 8M space  
                           315*	# Fields for SCY (Primary wait states, bits [24:27])
                           316*	    .equ OR0SCY_1,  0x00000010 # 0x1    
                           317*	    .equ OR0SCY_2,  0x00000020 # 0x2    
                           318*	    .equ OR0SCY_3,  0x00000030 # 0x3    
                           319*	    .equ OR0SCY_4,  0x00000040 # 0x4  
                           320*	    .equ OR0SCY_5,  0x00000050 # 0x5
                           321*	# Fields for BSCY (Secondary wait states, bits [29:30])
                           322*	    .equ OR0BSCY_0, 0x00000000 # 0x0
                           323*	    .equ OR0BSCY_1, 0x00000002 # 0x1
                           324*	    .equ OR0BSCY_2, 0x00000004 # 0x2
                           325*	    .equ OR0BSCY_3, 0x00000006 # 0x3
                           326*	
                           327*	# Define the addresses for the Chip Select 0 registers
                           328*	    .equ CS0BRREG,   0xC3F84010
                           329*	    .equ CS0ORREG,   0xC3F84014
                           330*	
                           331*	#*************************************************************
                           332*	# Define parameter variables for SRAM download control
                           333*	# Starting SRAM address defined in the linker file 
                           334*	#     __SRAM_LOAD set to ADDR(.heap) defined in the linker file 
                           335*	# Number of opcodes to be loaded defined in the linker file
                           336*	#     __SRAM_LOAD_SIZE set to SIZEOF(.flash_data) defined in the linker file
                           337*	# Instruction pointer advance to next loaded opcode loaded into SRAM
                           338*	    .equ IP_ADVANCE,  4   # Offset to next opcode loaded into SRAM
                           339*	
                           340*	# Copy of ROM data into ".data" and ".sdata" sections of SRAM
                           341*	#   __DATA_ROM set to ROM address for loading into ".data" of SRAM
                           342*	#   __SDATA_ROM set to ROM address for loading into ".sdata" of SRAM
                           343*	#   __ROM_COPY_SIZE number of bytes to be loaded into SRAM from ROM
                           344*	    .equ CPY_OFFSET,  1   # Byte offset to next address and decrement 
                           345*	#                         #  the ROM_COPY_SIZE
                           346*	
                           347*	#*************** MMU initialization constants ****************
                           348*	
                           349*	#********* TLB address offsets *********
                           350*	
                           351*	# Offset addresses for use in defining beginning of flash blocks
                           352*	    .equ OFFSET_1M,    0x00100000  #   1 MB 
                           353*	    .equ OFFSET_768K,  0x000C0000  # 768 KB 
                           354*	    .equ OFFSET_512K,  0x00080000  # 512 KB 
                           355*	    .equ OFFSET_256K,  0x00040000  # 256 KB 
                           356*	    .equ OFFSET_64K,   0x00010000  #  64 KB 
                           357*	    .equ OFFSET_4K,    0x00001000  #   4 KB 
                           358*	
                           359*	# Definitions for MMU (Memory Management Unit Registers) 
                           360*	
                           361*	#*************************************************************
                           362*	# Definitions for MAS0 (MMU Assist Register 0) 
                           363*	# bit reference: MAS0AVAL ((tlbsel<<28)|(eselcam<<16))
                           364*	

                                   Fri Jul 04 14:35:16 2025           Page 9
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                           365*	# Fields for MAS0 (MMU Assist Register 0 ) 
                           366*	# Field for TLBSEL  (TLB Select, bits [2:3]) 
                           367*	    .equ TLB_SELECT,  0x10000000   # Always 0x01 for MPC5554
                           368*	# Fields for ESELCAM (Entry Select for TLB CAM, bits [11:15])
                           369*	    .equ TLB_ENTRY0,  0x00000000   # Select TLB Entry slot 0
                           370*	    .equ TLB_ENTRY1,  0x00010000   # Select TLB Entry slot 1
                           371*	    .equ TLB_ENTRY2,  0x00020000   # Select TLB Entry slot 2
                           372*	    .equ TLB_ENTRY3,  0x00030000   # Select TLB Entry slot 3  
                           373*	    .equ TLB_ENTRY4,  0x00040000   # Select TLB Entry slot 4  
                           374*	    .equ TLB_ENTRY5,  0x00050000   # Select TLB Entry slot 5  
                           375*	    .equ TLB_ENTRY6,  0x00060000   # Select TLB Entry slot 6  
                           376*	    .equ TLB_ENTRY7,  0x00070000   # Select TLB Entry slot 7  
                           377*	    .equ TLB_ENTRY8,  0x00080000   # Select TLB Entry slot 8  
                           378*	    .equ TLB_ENTRY9,  0x00090000   # Select TLB Entry slot 9  
                           379*	    .equ TLB_ENTRY10, 0x000A0000   # Select TLB Entry slot 10 
                           380*	    .equ TLB_ENTRY11, 0x000B0000   # Select TLB Entry slot 11 
                           381*	    .equ TLB_ENTRY12, 0x000C0000   # Select TLB Entry slot 12 
                           382*	    .equ TLB_ENTRY13, 0x000D0000   # Select TLB Entry slot 13 
                           383*	    .equ TLB_ENTRY14, 0x000E0000   # Select TLB Entry slot 14 
                           384*	    .equ TLB_ENTRY15, 0x000F0000   # Select TLB Entry slot 15 
                           385*	    .equ TLB_ENTRY16, 0x00100000   # Select TLB Entry slot 16 
                           386*	    .equ TLB_ENTRY17, 0x00110000   # Select TLB Entry slot 17 
                           387*	    .equ TLB_ENTRY18, 0x00120000   # Select TLB Entry slot 18 
                           388*	    .equ TLB_ENTRY19, 0x00130000   # Select TLB Entry slot 19 
                           389*	    .equ TLB_ENTRY20, 0x00140000   # Select TLB Entry slot 20 
                           390*	    .equ TLB_ENTRY21, 0x00150000   # Select TLB Entry slot 21 
                           391*	    .equ TLB_ENTRY22, 0x00160000   # Select TLB Entry slot 22 
                           392*	    .equ TLB_ENTRY23, 0x00170000   # Select TLB Entry slot 23 
                           393*	
                           394*	#*************************************************************
                           395*	# Definitions for MAS1 (MMU Assist Register 1) 
                           396*	# bit reference: MAS1AVAL ((valid<<31)| (iprot<<30)|
                           397*	#                      (tid<<16)|(ts<<12)|(tsize<<8))
                           398*	
                           399*	# Fields for MAS1 (MMU Assist Register 1 ) 
                           400*	# Field for V (Valid, bit [0])
                           401*	    .equ TLB_ENTRY_INVALID,   0x00000000  # 0x0   
                           402*	    .equ TLB_ENTRY_VALID,     0x80000000  # 0x1   
                           403*	# Field for IPROT (Invalidation Protection, bit [1]) 
                           404*	    .equ ENTRY_NOT_PROTECTED, 0x00000000  # 0x0 # From Invalidation  
                           405*	    .equ ENTRY_PROTECTED,     0x40000000  # 0x1 # From Invalidation  
                           406*	# Field for TID (Process ID, bits [8:15])
                           407*	    .equ GLOBAL_MATCH,        0x00000000  # 0x0 # Match all process IDs  
                           408*	# Field for TS (Translation address space, bit [19])
                           409*	    .equ TS_IS_COMPARE,       0x00000000  # 0x0 # Match entry to instruction space  
                           410*	    .equ TS_DS_COMPARE,       0x00001000  # 0x1 # Match entry to data space 
                           411*	# Field for TSIZE (TLB entry page size, bits [20:23])
                           412*	    .equ TSIZ_4K,    0x00000100  # 0x1 # TLB page size =  4K bytes 
                           413*	    .equ TSIZ_16K,   0x00000200  # 0x2 # TLB page size = 16K bytes 
                           414*	    .equ TSIZ_64K,   0x00000300  # 0x3 # TLB page size = 64K bytes 
                           415*	    .equ TSIZ_256K,  0x00000400  # 0x4 # TLB page size =256K bytes 
                           416*	    .equ TSIZ_1M,    0x00000500  # 0x5 # TLB page size =  1M bytes 
                           417*	    .equ TSIZ_2M,    0x00000580  # 0x58 # TLB page size =  2M bytes 
                           418*	    .equ TSIZ_4M,    0x00000600  # 0x6 # TLB page size =  4M bytes 
                           419*	    .equ TSIZ_16M,   0x00000700  # 0x7 # TLB page size = 16M bytes 
                           420*	    .equ TSIZ_64M,   0x00000800  # 0x8 # TLB page size = 64M bytes 

                                   Fri Jul 04 14:35:16 2025           Page 10
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                           421*	    .equ TSIZ_128K,  0x00000380  # 0x38 # TLB page size = 128K bytes    
                           422*	    .equ TSIZ_256M,  0x00000900  # 0x9 # TLB page size = 256M bytes 
                           423*	#*************************************************************
                           424*	# Definitions for MAS2 (MMU Assist Register 2) 
                           425*	# bit reference: MAS2AVAL((epn<<12)|(w<<4)|(i << 3)|
                           426*	#                         (m << 2)|(g << 1)|(e)) 
                           427*	
                           428*	# Fields for MAS2 (MMU Assist Register 2 ) 
                           429*	# The EPN field is defined above with global address space 
                           430*	# EPN (effective page number, bits [0:19]) 
                           431*	# Field for W (Write-through required, bit [27]) 
                           432*	    .equ CACHE_WRITE_BACK, 0x00000000  # 0x0 # Stores write-back to cache 
                           433*	    .equ CACHE_WRITE_THRU, 0x00000010  # 0x1 # Stores write through to memory    
                           434*	    .equ CACHE_ACTIVE,     0x00000000  # 0x0 # Cache is active for TLB entry 
                           435*	# Field for I (Cache Inhibited, bit [28])
                           436*	    .equ CACHE_INHIBIT,    0x00000008  # 0x1 # Cache is inhibited for TLB entry 
                           437*	# Field for M (Memory Coherence required, bit [29])
                           438*	    .equ MEM_NO_COHERENCE, 0x00000000  # 0x0 # Only valid setting for MPC5554 
                           439*	    .equ MEM_COHERENCE,    0x00000004  # 0x1 # Not valid--ignored on MPC5554 
                           440*	# Field for G (Page Guarded, bit [30])
                           441*	    .equ PAGE_NOT_GUARDED, 0x00000000  # 0x0 # Cache page not guarded  
                           442*	    .equ PAGE_GUARDED,     0x00000002  # 0x1 # Cache page guarded   
                           443*	# Field for E (Endianess, bit [31])
                           444*	    .equ PAGE_BIG_ENDIAN,  0x00000000  # 0x0 # Big endian byte order 
                           445*	    .equ PAGE_LTL_ENDIAN,  0x00000001  # 0x1 # True little endian byte order  
                           446*	
                           447*	#*************************************************************
                           448*	# Definitions for MAS3 (MMU Assist Register 3) 
                           449*	# bit reference: MAS3AVAL ((rpn<<12)|(permissions))
                           450*	
                           451*	# Fields for MAS3 (MMU Assist Register 3 ) 
                           452*	# The RPN field is defined above with global address space 
                           453*	# RPN == real page number 
                           454*	
                           455*	# Field for U0-U3 (USR bits, bits [22:25])
                           456*	    .equ MAS3_USR0,      0x00000000  # 0x0  # User bit value =0000 
                           457*	# Field for UX (User Execute Access, bit [26])   
                           458*	    .equ USR_NO_EXECUTE, 0x00000000  # 0x0  # User cannot execute code 
                           459*	    .equ USR_EXECUTE,    0x00000020  # 0x1  # User executable permission 
                           460*	# Field for SX (Supervisor Execute Access, bit [27])
                           461*	    .equ SUP_NO_EXECUTE, 0x00000000  # 0x0  # Supervisor cannot execute code 
                           462*	    .equ SUP_EXECUTE,    0x00000010  # 0x1  # Supervisor executable permission
                           463*	# Field for UW (User Write Access, bit [28]) 
                           464*	    .equ USR_NO_WRITE,   0x00000000  # 0x0  # User cannot write code 
                           465*	    .equ USR_WRITE,      0x00000008  # 0x1  # User write permission 
                           466*	# Field for SW (Supervisor Write Access, bit [29])
                           467*	    .equ SUP_NO_WRITE,   0x00000000  # 0x0  # Supervisor cannot write code 
                           468*	    .equ SUP_WRITE,      0x00000004  # 0x1  # Supervisor write permission 
                           469*	# Field for UR (User Read Access, bit [30])
                           470*	    .equ USR_NO_READ,    0x00000000  # 0x0  # User cannot read code 
                           471*	    .equ USR_READ,       0x00000002  # 0x1  # User read permission 
                           472*	# Field for SR (Supervisor Read Access, bit [31])
                           473*	    .equ SUP_NO_READ,    0x00000000  # 0x0  # Supervisor cannot read code 
                           474*	    .equ SUP_READ,       0x00000001  # 0x1  # Supervisor read permission 
                           475*	
                           476*	# Field to define special cases - all access and read+execute, bits [26:31]

                                   Fri Jul 04 14:35:16 2025           Page 11
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                           477*	    .equ READWRITE,        0x0000000F  # 0x0f read, write permission only
                           478*	    .equ READWRITEEXECUTE, 0x0000003F  # 0x3f read, write and execute permission       
                           479*	    .equ READEXECUTE,      0x00000033  # 0x33 read and execute permission only        
                           480*	# bits [0:19] = RPN: Real page number. Same format as EPN. Use RPN        
                           481*	#                   identical to EPN if identical mapping of     
                           482*	#                   effective to physical address.                                         
                           483*	# bits [26:31] = (UX,SX,UW,SW,UR,SR):                                      
                           484*	#             Permission bits. User and supervisor read,                 
                           485*	#             write, and execute permission bits. See                    
                           486*	#             Table 10-3 [1] for more information on                     
                           487*	#             the page permission bits as they are                       
                           488*	#              defined by Book E.                                        
                           489*	#                                                                        
                           490*	
                           491*	#**************************************************************************
                           492*	#           Set up  ERRLOG save error log register                         
                           493*	#**************************************************************************
                           494*	
                           495*	# Definitions for ERRLOG (Error Log Register) 
                           496*	
                           497*	# Fields for Error Log Register 
                           498*	    .equ MFDERR_N,     0x00000000  # 0x0
                           499*	    .equ MFDERR_Y,     0x80000000  # 0x1
                           500*	    .equ LOCINTERR_N,  0x00000000  # 0x0
                           501*	    .equ LOCINTERR_Y,  0x40000000  # 0x1
                           502*	    .equ LOLINTERR_N,  0x00000000  # 0x0
                           503*	    .equ LOLINTERR_Y,  0x20000000  # 0x1
                           504*	    .equ PLLNORMERR_N, 0x00000000  # 0x0
                           505*	    .equ PLLNORMERR_Y, 0x10000000  # 0x1
                           506*	
                           507*	# Machine check errors    
                           508*	    .equ ERRLOGCPPER,  0x02000000  # Error log CP_PERR error input
                           509*	    .equ ERRLOGCPER,   0x01000000  # Error log CPERR error input
                           510*	    .equ ERRLOGXCPT,   0x00800000  # Error log exception error input
                           511*	    .equ ERRLOGWRT,    0x00400000  # Error log write error input
                           512*	
                           513*	# Error Log definitions used by cfg_FMPLL and cfg_MCHK_hndlr
                           514*	    .equ ERRLOG_MFDERR, (MFDERR_Y)         # Error on change of FMPLL[MFD]
                           515*	    .equ ERRLOG_LOCINTERR, (LOCINTERR_Y)   # FMPLL fails to lock after LOC interrupt enable
                           516*	    .equ ERRLOG_LOLINTERR, (LOLINTERR_Y)   # FMPLL fails to lock after LOL interrupt enable
                           517*	    .equ ERRLOG_PLLNORMERR, (PLLNORMERR_Y) # FMPLL not in normal mode (loss of clock)
                           518*	    
                           519*	    .equ ERRLOG_PUSHERR, (ERRLOGCPPER)     # Machine check cache push parity error
                           520*	    .equ ERRLOG_PARERR, (ERRLOGCPER)       # Machine check cache parity error
                           521*	    .equ ERRLOG_XERR, (ERRLOGXCPT)         # Machine check from exception error
                           522*	    .equ ERRLOG_WRTERR, (ERRLOGWRT)        # Machine check from write error
                           523*	
                           524*	# Error Log address locations
                           525*	    .equ ERRLOGAD_DMATCD_00,   0xFFF45000  # DMA TCD00
                           526*	    .equ ERRLOGAD_DMATCD_63,   0xFFF457E0  # DMA TCD63
                           527*	    .equ ERRLOGAD_ETPUPRAM_LO, 0xC3FC8000  # Start of eTPU Parameter RAM
                           528*	    .equ ERRLOGAD_ETPUPRAM_HI, 0xC3FC8B00  # Locate at end of eTPU Parameter RAM
                           529*	
                           530*	#**************************************************************
                           531*	
                             0*	    .include "asm_ghs_macros.inc"

                                   Fri Jul 04 14:35:16 2025           Page 12
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                             1*	#************************************************************************
                             2*	#* FILE NAME: asm_ghs_macros.inc            
                             3*	#*
                             4*	#* This file contains macro definitions for PPC ASM to VLE translation
                             5*	#* 
                             6*	#*========================================================================
                             7*	#* GK! 06/12/2011
                             8*	#************************************************************************
                             9*	
                            10*	
                            11*	# macro for subi, use e_add16i with -param 
                            12*	.macro subi RT,RA,SI
                            13*	    e_add16i RT,RA,-SI
                            14*	.endm
                            15*	
                            16*	# ATTENTION: macro for ori, e_or2i uses only 1 register as parameter !!!
                            17*	.macro ori RA,RS,UI
                            18*	    e_or2i RA,UI
                            19*	.endm
                            20*	
                            21*	# macro
                            22*	.macro addic. RA,RS,UI
                            23*	    e_add2i. RA,UI
                            24*	.endm
                            25*	
                            23*	
                            24*	#*************** Runtime Variables *****************
                            25*	#  These runtime variables are used in __start.s
                            26*	# main(), assembly cfg_* calls, and cfg_mpc5500_ccd() need to  **
                            27*	# have far absolute addressing if flashing to ROM with         **
                            28*	# distant addresses on the MPC5500.                            **
                            29*	# The .equ statements below should be set to "1" to be valid   **
                            30*	#  and set to "0" to be inactive.                              **
                            31*	
                            32*	
                            33*	  	.equ SRAM_TEST,        1  # Used to enable SRAM test
                            34*	  	.equ VSRAM_EXIST,      1  # Used to manage the VSRAM memory ram if configured
                            35*	
                            36*	    .equ FAR_ADDRESS,      0  # Used for a FAR_ADDRESS call
                            37*	    .equ FAR_ADDRESS_MAIN, 1  # Used for a FAR_ADDRESS call to main
                            38*	    .equ SIM_VER,          0  # Used with the Code Warrior simulator
                            39*	    .equ VLE_ENABLE,       1  # Enable the VLE instruction set (Pictus has only VLE mode)
                            40*	    
                            41*	    .equ FLSH_RUN,         1  # Set to (1) for code in Flash
                            42*	#                             #     to (0) for code in SRAM
                            43*	
                            44*	    .equ EXT_BOOT,         0  # Set to (1) for External boot. 
                            45*	#                             #  BAM sets up external bus and CS[0]
                            46*	# Reset Configuration Half Word Variables **
                            47*	    .equ RCHW_WTE, WDOG_DISABLE # Watchdog control at reset
                            48*	    .equ RCHW_PS0, CS0_32BIT    # CS0 data port size at reset
                            49*	    .equ BOOT_ID,  MPC5500_ID   # Valid boot ID for MPC5500 devices
                            50*	
                            51*	#*************************************************************
                            52*	#******** Special Initialization Option Constants  ***********
                            53*	# The "I_" prefixed variables are initialization defines      

                                   Fri Jul 04 14:35:16 2025           Page 13
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                            54*	#  Set the value to one ("1") to enable the option.
                            55*	#  Or, set the value to zero ("0") to disable the option.
                            56*	
                            57*	    .equ I_LOCEN,    1 # Set loss of clock enable function
                            58*	    .equ I_BCKUPCLK, 1 # Enable backup clock on loss of clock
                            59*	
                            60*	# Mutually exclusive pair (one set to "0", one set to "1"):
                            61*	    .equ I_LOSSCRST, 0 # Enable reset on loss of clock 
                            62*	    .equ I_LOCINT,   1 # Enable interrupt on loss of clock
                            63*	# Mutually exclusive pair (one set to "0", one set to "1"):
                            64*	    .equ I_LOSSLRST, 0 # Enable reset on loss of lock  
                            65*	    .equ I_LOLINT,   1 # Enable interrupt on loss of lock 
                            66*	
                            67*	
                            68*	# To match MMU entry size:
                            69*	    .equ I_SRAM_SIZE,   SIZE_64K   #   64 KB RAM Size
                            70*	    .equ I_XSRAM_SIZE,  SIZE_512K  #  512 KB External RAM Size
                            71*	    .equ I_XSRAM_SPACE, SIZE_4M    #    4 MB External RAM Space
                            72*	
                            73*	#*************************************************************
                            74*	#      User Defined Options
                            75*	#  These values should be modified based on user requirements
                            76*	#*************************************************************
                            77*	# Cache definitions used by cfg_CACHE and cfg_STACK:
                            78*	#  Copy back mode (CWM=1) and Push buffer disabled (DPB=1) is
                            79*	#   required by errata #32 and #34 to allow MMU control of cache.
                            80*	#   These errata may go away in the future (see current errata)
                            81*	    #.equ CACHE_CLEAR,(CLFC_NO_OP | CINV_INV_OP | CE_DISABLE) ANDORRA
                            82*	    #.equ CACHE_SETTINGS, (CHECKERR_ENABLE | CHECKERR_EDC | CHECKERR_AUTCOR | CORG_32S_4W | CE_ENABLE)  ANDORRA
                            83*	# K2
                            84*	    .equ CACHE_CLEAR,    (ICLOINV_INV_OP | ICINV_INV_OP | ICE_DISABLE)
                            85*	    .equ CACHE_SETTINGS, (ICHECKERR_ENABLE | ICHECKERR_AUTCOR | ICE_ENABLE)   
                            86*	#*******************************************************************
                            87*	# Flash definitions used by cfg_FLASH:
                            88*	#     Internal Flash: FLASH_BIUCR (0xC3F8_801C)    
                            89*	# ap: mod
                            90*	#    .equ FLASH_SETTINGS, (EBI_PREFTCH_ON | APC_3 | WWSC_1 | RWSC_2 | IPFEN_ANY | PFLIM_2 | BFEN_EN)
                            91*	    .equ FLASH_SETTINGS, (EBI_PREFTCH_ON | APC_2 | WWSC_1 | RWSC_2 | IPFEN_ANY | PFLIM_2 | BFEN_EN)
                            92*	#     External Flash: CS0 OR settings used by cfg_FLASH:
                            93*	#     The next line is commented out as an example of optimizing
                            94*	#      external Flash boot times.
                            95*	#    .equ CS0_OR_OPTIONS, (AMASK_8M | OR0SCY_2 | OR0BSCY_0)
                            96*	    
                            97*	#*******************************************************************
                            98*	# FMPLL definitions used by cfg_FMPLL
                            99*	#  Set the internal clock to 32 MHz with MFD=16, and RFD=4.
                           100*	#  Setting 1 is intended to only change the MFD bit with no change to the RFD bit.
                           101*	    .equ FSYS_60,         0
                           102*	    .equ FSYS_80,         1
                           103*	    .equ FSYS_128,        0
                           104*	    
                           105*	    #.if FSYS_128
                           106*	    #.equ FMPLL_SYNCR_SETTING1, (MFD_16 | RFD_4 | LOCEN_EN)  # MFD=16, RFD=4 for  32MHz  #OK???
                           107*	    #.endif
                           108*	
                           109*	    .if FSYS_80

                                   Fri Jul 04 14:35:16 2025           Page 14
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                           110*	    .equ FMPLL_SYNCR_SETTING1, (MFD_8 | PREDIV_3 | RFD_1 | LOCEN_EN)  # for 32 MHz   #OK!!!
                           111*	    .endif
                           112*	    
                           113*	    .if FSYS_60
                           114*	    .equ FMPLL_SYNCR_SETTING1, (MFD_10 | RFD_4 | LOCEN_EN)  # MFD=10, RFD=4 for 30 MHz #OK!!!
                           115*	    .endif
                           116*	
                           117*	#  removed by Akhela Start
                           118*	#  Set the internal clock to 128 MHz with MFD=16, and RFD=1.
                           119*	#  This sequence sets the RFD to divide-by-1 in the FMPLL_SYNCR Register.
                           120*	#  Setting 2 is intended to only change the RFD bit with no change to the MFD bit.
                           121*	#    .equ FMPLL_SYNCR_SETTING2, (MFD_16 | RFD_1 | LOCEN_EN)  # MFD=16, RFD=1 for 128MHz
                           122*	#  removed By Akhela End
                           123*	
                           124*	#  Set the internal clock to 80 MHz with MFD=10, and RFD=1.
                           125*	#  This sequence sets the RFD to divide-by-1 in the FMPLL_SYNCR Register.
                           126*	#           Fref * (MFD + 4)
                           127*	#  Fsys = ---------------------
                           128*	#         (PREDIV + 1)* 2^(RFD)
                           129*	
                           130*	    #.if FSYS_128
                           131*	    #.equ FMPLL_SYNCR_SETTING2, (MFD_16 | RFD_1 | LOCEN_EN)  # MFD=16, RFD=1 for 128MHz   #OK???
                           132*	    #.endif
                           133*	
                           134*	    .if FSYS_80
                           135*	    .equ FMPLL_SYNCR_SETTING2, (MFD_20 | PREDIV_3 | RFD_1 | LOCEN_EN)  # MFD_20=16, PREDIV_3=2, RFD_1=0 for 80 MHz #OK!!!
                           136*	    .endif
                           137*	
                           138*	    .if FSYS_60
                           139*	    .equ FMPLL_SYNCR_SETTING2, (MFD_10 | RFD_2 | LOCEN_EN)  # MFD_10=6, RFD_2=1 for 60 MHz  #OK!!!
                           140*	    .endif
                           141*	    
                           142*	#*******************************************************************
                           143*	# SIU definitions used by cfg_FMPLL
                           144*	# The SIU definition below will generate a reset of the device when used.
                           145*	#  A system reset or an external reset will result depending on settings.
                           146*	    .equ SIU_SRCR_SYSRST, (SSR_SYSRST | SER_NORST | CRE_NO)    
                           147*	
                           148*	#*******************************************************************
                           149*	# ERRLOGREG (Error Log Register) address definition
                           150*	    .equ ERRLOGREG, ERRLOGAD_ETPUPRAM_HI # Assembler token address
                           151*	    
                           152*	#*********************************************************************
                           153*	
                           154*	
                           155*	
                           156*	
                           157*	
                            46	
                            47	
                            48	    .globl cfg_MMU
                            49	    .globl cfg_CACHE      
                            50	    .globl disable_CACHE 
                            51	 
                            52	#################################################
                            53	#       This is the start of the .init section.

                                   Fri Jul 04 14:35:16 2025           Page 15
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                            54	
                            55	    .if __PEGNU__
                            56	    .section ".init","ax" # The "ax" is required to generate "non-text" code
                            57	    .endif
                            58	
                            59	    .if __GRNHS__
                            60	    .section .init,"axv"     # The "axv" generates symbols for debug
                            61	    .vle
                            62	    .endif
                            63	
                            64	    .if __DIABCC__
                            65	    .section .init,c      # The "c" generates symbols for debug
                            66	    .endif
                            67	    
                            68	     .if __CWWRKS__
                            69	    .section .init,text   # The "text" generates symbols for debug
                            70	    .endif
                            71	
                            72	#*********************************************************************/
                            73	#*************************************************************************/
                            74	#                        MMU Functions                                   */
                            75	#*************************************************************************/
                            76	
                            77	#*****************************************************************************/
                            78	# FUNCTION     : cfg_MMU                                                     */
                            79	# PURPOSE      : This function modifies the MMU TLB (translation lookaside   */
                            80	#                 buffer) table by writing to the appropriate MAS registers. */
                            81	# INPUT NOTES  : Requires SPRs defined and a data table for the TLB entries  */
                            82	#                mmu_tlb0 through mmu_tlb11, mmu_tlb15 from                  */
                            83	#                mpc5500_usrdefs.inc.                                        */
                            84	# RETURN NOTES : None                                                        */
                            85	# WARNING      : Registers used: R3,R5. Commands "msync" and "isync" are not */
                            86	#                required around the tlbwe since we are at configuration and */
                            87	#                 other background operations cannot be active.              */
                            88	#*****************************************************************************/
                            89	
                            90	cfg_MMU:
                            91	
                            92	#***************************************************/
                            93	#     setup MMU                                    */
                            94	#***************************************************/
                            95	
                            96	# Configure the TLB0 PBRIDGE_B size to 1M.    
                            97	#default configuration
                            98	
00000000 7060e000           99	    e_lis   r3, mmu_tlb0@h     # base address of MAS Constants
00000004 7060c000          100	    e_or2i r3,mmu_tlb0@l
00000008 50a30000          101	    e_lwz   r5,0(r3)           # Get MAS0 value
0000000c 7cb09ba6          102	    mtspr mas0,r5            # mtspr MAS0,r5
00000010 18a30204          103	    e_lwzu  r5,4(r3)           # Get MAS1 value
00000014 7cb19ba6          104	    mtspr mas1,r5            # mtspr MAS1,r5
00000018 18a30204          105	    e_lwzu  r5,4(r3)           # Get MAS2 value
0000001c 7cb29ba6          106	    mtspr mas2,r5            # mtspr MAS2,r5
00000020 18a30204          107	    e_lwzu  r5,4(r3)           # Get MAS3 value
00000024 7cb39ba6          108	    mtspr mas3,r5            # mtspr MAS3,r5
00000028 7c0007a4          109	    tlbwe                    # Write the entry to the TLB 

                                   Fri Jul 04 14:35:16 2025           Page 16
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                           110	
                           111	# Configure the TLB1 Flash (1) size to 2M
                           112	
0000002c 7060e000          113	    e_lis   r3, mmu_tlb1@h     # base address of MAS Constants
00000030 7060c000          114	    e_or2i r3,mmu_tlb1@l 
00000034 50a30000          115	    e_lwz   r5,0(r3)           # Get MAS0 value
00000038 7cb09ba6          116	    mtspr mas0,r5            # mtspr MAS0,r5
0000003c 18a30204          117	    e_lwzu  r5,4(r3)           # Get MAS1 value
00000040 7cb19ba6          118	    mtspr mas1,r5            # mtspr MAS1,r5
00000044 18a30204          119	    e_lwzu  r5,4(r3)           # Get MAS2 value
00000048 7cb29ba6          120	    mtspr mas2,r5            # mtspr MAS2,r5
0000004c 18a30204          121	    e_lwzu  r5,4(r3)           # Get MAS3 value
00000050 7cb39ba6          122	    mtspr mas3,r5            # mtspr MAS3,r5
00000054 7c0004ac          123	    msync                    # synchronize for running out of Flash
00000058 7c0007a4          124	    tlbwe                    # Write the entry to the TLB 
0000005c 0001              125	    se_isync                    # synchronize for running out of Flash 
                           126	
                           127	
                           128	# Configure the TLB2 EBI size to 16MB.  EBI is not present in MPC5642A, but is present in MPC5644A
                           129	#default configuration
                           130	
0000005e 7060e000          131	    e_lis   r3, mmu_tlb2@h     # base address of MAS Constants
00000062 7060c000          132	    e_or2i r3, mmu_tlb2@l
00000066 50a30000          133	    e_lwz   r5,0(r3)           # Get MAS0 value
0000006a 7cb09ba6          134	    mtspr mas0,r5            # mtspr MAS0,r5
0000006e 18a30204          135	    e_lwzu  r5,4(r3)           # Get MAS1 value
00000072 7cb19ba6          136	    mtspr mas1,r5            # mtspr MAS1,r5
00000076 18a30204          137	    e_lwzu  r5,4(r3)           # Get MAS2 value
0000007a 7cb29ba6          138	    mtspr mas2,r5            # mtspr MAS2,r5
0000007e 18a30204          139	    e_lwzu  r5,4(r3)           # Get MAS3 value
00000082 7cb39ba6          140	    mtspr mas3,r5            # mtspr MAS3,r5
00000086 7c0007a4          141	    tlbwe                    # Write the entry to the TLB 
                           142	
                           143	# Configure the TLB3 SRAM size to 128KB. 
                           144	
0000008a 7060e000          145	    e_lis   r3, mmu_tlb3@h     # base address of MAS Constants
0000008e 7060c000          146	    e_or2i r3, mmu_tlb3@l
00000092 50a30000          147	    e_lwz   r5,0(r3)           # Get MAS0 value
00000096 7cb09ba6          148	    mtspr mas0,r5            # mtspr MAS0,r5
0000009a 18a30204          149	    e_lwzu  r5,4(r3)           # Get MAS1 value
0000009e 7cb19ba6          150	    mtspr mas1,r5            # mtspr MAS1,r5
000000a2 18a30204          151	    e_lwzu  r5,4(r3)           # Get MAS2 value
000000a6 7cb29ba6          152	    mtspr mas2,r5            # mtspr MAS2,r5
000000aa 18a30204          153	    e_lwzu  r5,4(r3)           # Get MAS3 value
000000ae 7cb39ba6          154	    mtspr mas3,r5            # mtspr MAS3,r5
000000b2 7c0007a4          155	    tlbwe                    # Write the entry to the TLB 
                           156	
                           157	# Configure the TLB4 PBRIDGE_B size to 1M. 
                           158	#default configuration
                           159	    
000000b6 7060e000          160	    e_lis   r3, mmu_tlb4@h     # base address of MAS Constants
000000ba 7060c000          161	    e_or2i r3, mmu_tlb4@l
000000be 50a30000          162	    e_lwz   r5,0(r3)           # Get MAS0 value
000000c2 7cb09ba6          163	    mtspr mas0,r5            # mtspr MAS0,r5
000000c6 18a30204          164	    e_lwzu  r5,4(r3)           # Get MAS1 value
000000ca 7cb19ba6          165	    mtspr mas1,r5            # mtspr MAS1,r5

                                   Fri Jul 04 14:35:16 2025           Page 17
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
000000ce 18a30204          166	    e_lwzu  r5,4(r3)           # Get MAS2 value
000000d2 7cb29ba6          167	    mtspr mas2,r5            # mtspr MAS2,r5
000000d6 18a30204          168	    e_lwzu  r5,4(r3)           # Get MAS3 value
000000da 7cb39ba6          169	    mtspr mas3,r5            # mtspr MAS3,r5
000000de 7c0007a4          170	    tlbwe  
                           171	
000000e2 0004              172	    se_blr
                           173	# End of cfg_MMU 
                           174	
                           175	##*************************************************************************/
                           176	
                           177	#************************************************************************/
                           178	# FUNCTION     : cfg_CACHE                                             
                           179	# PURPOSE      : This function initializes the CACHE by invalidating and 
                           180	#                  then enabling the cache.                             
                           181	# INPUT NOTES  : CACHE_CLEAR, CACHE_SETTINGS, L1CSR0                     
                           182	# RETURN NOTES : None                                                    
                           183	# WARNING      : Registers used: R5,R8,R9,R10                           
                           184	
                           185	cfg_CACHE:
                           186	
000000e4 7c000146          187	    wrteei 0
                           188	
                           189	# To activate cache invalidate operation,
                           190	# place a "1" in the CINV bit location.  (L1CSR1[30])
                           191	#  This operation takes 134 cycles to complete
000000e8 70a0e000          192	    e_lis   r5, CACHE_CLEAR@h        # Load upper L1CSR1 (0x0) into R5
000000ec 70a0c012          193	    e_or2i r5, CACHE_CLEAR@l    # Load lower L1CSR1 (CINV bit) into R5
000000f0 0001              194	    se_isync                          # Required before changing the CE bit to
000000f2 7c0004ac          195	    msync    
000000f6 7cb3fba6          196	    mtspr 1011,r5                  # Move R5 to the L1CSR1 (SPR 1010)register.  
                           197	#                                  # This should start the CACHE invalidate
                           198	#                                  #  operation.
                           199	# Make sure that CINV is not active/finished
                           200	label_CINV_check:                  # Make sure that CINV is not active/finished
                           201	# The CINV mask bit will be compared to L1CSR1[30] CINV bit
000000fa 7100e000          202	    e_lis   r8, 0x0000               # Load upper CINV mask (0x0000) into R8
000000fe 7100c002          203	    e_or2i   r8, 0x0002           # Load lower L1CSR1[30] CINV mask bit into R8
                           204	CHECK_CINV:
00000102 7d33faa6          205	    mfspr r9, 1011               # Move the L1CSR1 register value to R9.
                           206	# "AND" the two registers together to check for active CINV bit
00000106 7d0a4839          207	    and.  r10,r8, r9
                           208	#                                  # The "." after "and" activates the condition register
0000010a 7a020000          209	    e_bne CHECK_CINV                 # Branch if not zero. CINV still=1.
                           210	
                           211	# Enable cache
0000010e 7cb3faa6          212	    mfspr r5, 1011               # Retrieve the L1CSR0 register value
                           213	    //oris  r5, r5, CACHE_SETTINGS@h # OR in CWM and DPB in upper L1CSR1[11:12]
00000112 70a0d001          214	    e_or2is  r5, CACHE_SETTINGS@h # OR in CWM and DPB in upper L1CSR1[11:12], MC tbv
00000116 70a0c021          215	    e_or2i r5, CACHE_SETTINGS@l # OR in cache enable bit L1CSR1[31]
0000011a 0001              216	    se_isync                          # Required before changing the CE bit to
0000011c 7c0004ac          217	    msync                          #  prevent disable/enable cache during access
00000120 7cb3fba6          218	    mtspr 1011, r5               # Return value to L1CSR1 to enable the cache
                           219	
00000124 7c008146          220	    wrteei 1
                           221	

                                   Fri Jul 04 14:35:16 2025           Page 18
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
00000128 0004              222	    se_blr   
                           223	# End of cfg_CACHE
                           224	
                           225	#************************************************************************/
                           226	# FUNCTION     : disable_CACHE                                             
                           227	# PURPOSE      : This function disables the CACHE by invalidating and 
                           228	#                  then disabling the cache.                             
                           229	# INPUT NOTES  : CACHE_CLEAR, CACHE_SETTINGS, L1CSR0                     
                           230	# RETURN NOTES : None                                                    
                           231	# WARNING      : Registers used: R5,R8,R9,R10                           
                           232	
                           233	disable_CACHE:
                           234	
0000012a 7c000146          235	wrteei 0
                           236	
                           237	# To activate cache invalidate operation,
                           238	# place a "1" in the CINV bit location.  (L1CSR0[30])
                           239	#  This operation takes 134 cycles to complete
0000012e 70a0e000          240	    e_lis   r5, CACHE_CLEAR@h        # Load upper L1CSR1 (0x0) into R5
00000132 70a0c012          241	    e_or2i   r5, CACHE_CLEAR@l    # Load lower L1CSR1 (CINV bit) into R5  
00000136 0001              242	    se_isync                          # Required before changing the CE bit to
00000138 7c0004ac          243	    msync    
0000013c 7cb3fba6          244	    mtspr 1011,r5                # Move R5 to the L1CSR1(SPR 1011)register.  
                           245	#                                  # This should start the CACHE invalidate
                           246	#                                  #  operation.
                           247	# Make sure that CINV is not active/finished
                           248	label_CINV_check2:                  # Make sure that CINV is not active/finished
                           249	# The CINV mask bit will be compared to L1CSR0[30] CINV bit
00000140 7100e000          250	    e_lis   r8, 0x0000               # Load upper CINV mask (0x0000) into R8
00000144 7100c002          251	    e_or2i r8, 0x0002           # Load lower L1CSR1[30] CINV mask bit into R8
                           252	CHECK_CINV2:
00000148 7d33faa6          253	    mfspr r9, 1011               # Move the L1CSR1 register value to R9.
                           254	# "AND" the two registers together to check for active CINV bit
0000014c 7d0a4839          255	    and.  r10,r8, r9
                           256	#                                  # The "." after "and" activates the condition register
00000150 7a020000          257	    e_bne CHECK_CINV2                 # Branch if not zero. CINV still=1.
                           258	
                           259	#DisableCache
00000154 70a0e000          260	    e_lis   r5, 0
00000158 0001              261	    se_isync                          # Required before changing the CE bit to
0000015a 7c0004ac          262	    msync    
0000015e 7cb3fba6          263	    mtspr 1011,r5                 # Move R5 to the L1CSR1(SPR 1011)register.     
                           264	
00000162 7c000146          265	    wrteei 0
                           266	
00000166 0004              267	    se_blr   
                           268	# End of cfg_CACHE
                           269	
                           270	
                           271	
                           272	##*************************************************************************/
                           273	# FUNCTION     : MMU DATA Tables                                          */
                           274	# PURPOSE      : This defines the MMU data tables for the TLB entries     */
                           275	#                which are set in the file mpc5500_asmcfg.s               */
                           276	# INPUT NOTES  : Requires that the TLB settings be in MPC5500_defs.inc    */
                           277	# RETURN NOTES : mmu_tlb0 [TLB0_MAS[0:3] through mmu_tlb11 [TLB0_MAS[0:3] */

                                   Fri Jul 04 14:35:16 2025           Page 19
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                           278	# WARNING      : Registers used: none. Section is: .rodata                */
                           279	##*************************************************************************/
                           280	
                           281	
                           282	# Solinas : 07/04/2005 Section declaration for MMU TLBs :
                           283	    
                           284	    .if __PEGNU__
                           285	    .section ".rodata" 
                           286	    .endif
                           287	
                           288	    .if __CWWRKS__ | __DIABCC__ | __GRNHS__
                           289	    .section .rodata
                           290	    .endif
                           291	        
                           292	    
                           293	#*************************************************************************/
                           294	#* DESCRIPTION:                                                          */  
                           295	#* This table contains definitions for the MPC564xA MMU TLB entries.      */
                           296	#* The bit definitions used in the TLB defines are located below.        */  
                           297	#* The second half of the file is the TLB setup code in mpc5500_asmcfg.s */
                           298	#*************************************************************************/ 
                           299	
                           300	#*** TLB DEFINES ***/
                           301	
                           302	#** TLB entry 0 - PBRIDGE_B set to 1M **
                           303	mmu_tlb0:
                           304	# TLB1_MAS0
00000000 10000000          305	    .long ( TLB_SELECT | TLB_ENTRY0 )
                           306	# TLB1_MAS1
00000004 c0000500          307	    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_1M )
                           308	# TLB1_MAS2
00000008 fff0000a          309	    .long ( PBRIDGEB_BASE_ADDR | CACHE_WRITE_BACK | CACHE_INHIBIT | MEM_NO_COHERENCE | PAGE_GUARDED | PAGE_BIG_ENDIAN )
                           310	# TLB1_MAS3
0000000c fff0003f          311	    .long ( PBRIDGEB_BASE_ADDR | READWRITEEXECUTE )
                           312	
                           313	#** TLB entry 1 - Internal FLASH set to 2MB **
                           314	mmu_tlb1:
                           315	# TLB1_MAS0
00000010 10010000          316	    .long ( TLB_SELECT | TLB_ENTRY1 )
                           317	# TLB1_MAS1
00000014 c0000580          318	    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_2M )
                           319	# TLB1_MAS2
00000018 00000000          320	    .long ( FLASH_BASE_ADDR | CACHE_WRITE_BACK | CACHE_ACTIVE | MEM_NO_COHERENCE | PAGE_NOT_GUARDED | PAGE_BIG_ENDIAN )
                           321	# TLB1_MAS3
0000001c 0000003f          322	    .long ( FLASH_BASE_ADDR | READWRITEEXECUTE )
                           323	
                           324	#** TLB entry 2 - External Bus Interface set to 16MB **   EBI is not present in MPC5642A, but is present in MPC5644A
                           325	mmu_tlb2:
                           326	# TLB2_MAS0
00000020 10020000          327	    .long ( TLB_SELECT | TLB_ENTRY2 )
                           328	# TLB2_MAS1
00000024 c0000700          329	    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_16M )
                           330	# TLB2_MAS2
00000028 20000000          331	    .long ( EXTBUSINT_BASE_ADDR | CACHE_WRITE_BACK | CACHE_ACTIVE | MEM_NO_COHERENCE | PAGE_NOT_GUARDED | PAGE_BIG_ENDIAN )
                           332	# TLB2_MAS3
0000002c 0000003f          333	    .long ( EXTBUSINT_PHY_ADDR | READWRITEEXECUTE )

                                   Fri Jul 04 14:35:16 2025           Page 20
                                   Fri May 23 11:35:03 2025   mpc5500_asmcfg_mmu_GHS.s
                           334	
                           335	
                           336	#** TLB entry 3 - Internal SRAM 128K **
                           337	mmu_tlb3:
                           338	# TLB2_MAS0
00000030 10030000          339	    .long ( TLB_SELECT | TLB_ENTRY3 )
                           340	# TLB2_MAS1
00000034 c0000380          341	    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_128K )
                           342	# TLB2_MAS2
00000038 40000008          343	    .long ( SRAM_BASE_ADDR | CACHE_WRITE_BACK | CACHE_INHIBIT | MEM_NO_COHERENCE | PAGE_NOT_GUARDED | PAGE_BIG_ENDIAN )
                           344	# TLB2_MAS3
0000003c 4000003f          345	    .long ( SRAM_BASE_ADDR | READWRITEEXECUTE )
                           346	
                           347	#** TLB entry 4 - PBRIDGE_A set to 1MB **
                           348	mmu_tlb4:
                           349	# TLB3_MAS0
00000040 10040000          350	    .long ( TLB_SELECT | TLB_ENTRY4)
                           351	# TLB3_MAS1
00000044 c0000500          352	    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_1M )
                           353	# TLB3_MAS2
00000048 c3f0000a          354	    .long ( PBRIDGEA_BASE_ADDR | CACHE_WRITE_BACK | CACHE_INHIBIT | MEM_NO_COHERENCE | PAGE_GUARDED | PAGE_BIG_ENDIAN )
                           355	# TLB3_MAS3
0000004c c3f0003f          356	    .long ( PBRIDGEA_BASE_ADDR | READWRITEEXECUTE )
                           357	
                           358	
