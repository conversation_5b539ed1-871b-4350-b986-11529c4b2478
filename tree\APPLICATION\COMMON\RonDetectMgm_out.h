/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      RonDetectMgm.h
 **  Date:          28-Feb-2022
 **
 **  Model Version: 1.1173
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_RonDetectMgm_h_
#define RTW_HEADER_RonDetectMgm_h_
#ifndef RonDetectMgm_COMMON_INCLUDES_
# define RonDetectMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* RonDetectMgm_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

 
/* Enumerated types definition */
typedef uint8_T enum_StRonDetect;
#define RD_INIT                        ((enum_StRonDetect)0U)    /* Default value */
#define RD_WAIT_STAB                   ((enum_StRonDetect)1U)
#define RD_STAB                        ((enum_StRonDetect)2U)
#define RD_TEST_STOP                   ((enum_StRonDetect)3U)
#define RD_INHERIT                     ((enum_StRonDetect)4U)
 
/* Model entry point functions */
extern void RonDetectMgm_initialize(void);

/* Exported entry point function */
extern void RonDetectMgm_100ms(void);

/* Exported entry point function */
extern void RonDetectMgm_EOA(void);

/* Exported entry point function */
extern void RonDetectMgm_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_EE_INTERFACE */
extern uint8_T FlgRonStoredEE;         /* '<S3>/Merge44' */

/* Ron stored in EE */
extern uint32_T OdomRonStartEE;        /* '<S1>/RonDetectMgm' */

/* Odometer value when Ron detection starts */
extern uint32_T OdomRonStopEE;         /* '<S1>/RonDetectMgm' */

/* Odometer value when Ron detection stops */
extern uint32_T SecRonStartEE;         /* '<S1>/RonDetectMgm' */

/* Seconds run-time value when Ron detection starts */
extern uint32_T SecRonStopEE;          /* '<S1>/RonDetectMgm' */

/* Seconds run-time value when Ron detection stops */
extern uint8_T FlgRonStoredFuel; 
extern uint8_T frs_rdm;
extern uint8_T frs_rdm_value1;
extern uint8_T frs_rdm_value;
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint32_T DKnockIntArg;          /* '<S1>/RonDetectMgm' */

/* KnockInt used as input argument for software component RonDetectCnt */
extern enum_StRonDetect StRonDetect;   /* '<S3>/Merge50' */

/* RonDetect main state */
extern enum_StRonDetect StRonDetectArg;/* '<S1>/RonDetectMgm' */

/* RonDetect main state used as input argument for software component RonDetectCnt */


/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'RonDetectMgm'
 * '<S1>'   : 'RonDetectMgm/EOA'
 * '<S2>'   : 'RonDetectMgm/PowerOn'
 * '<S3>'   : 'RonDetectMgm/Subsystem'
 * '<S4>'   : 'RonDetectMgm/T100ms'
 * '<S5>'   : 'RonDetectMgm/EOA/RonDetectCnt_Monitoring'
 * '<S6>'   : 'RonDetectMgm/EOA/RonDetectCnt_Reset'
 * '<S7>'   : 'RonDetectMgm/EOA/RonDetectEst_BackgroundInit'
 * '<S8>'   : 'RonDetectMgm/EOA/RonDetectEst_MonitoringInit'
 * '<S9>'   : 'RonDetectMgm/EOA/RonDetectEst_Recovery'
 * '<S10>'  : 'RonDetectMgm/EOA/RonDetectEst_ResetSecStartRun'
 * '<S11>'  : 'RonDetectMgm/EOA/RonDetectMgm'
 */

/*-
 * Requirements for '<Root>': RonDetectMgm
 */
#endif                                 /* RTW_HEADER_RonDetectMgm_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/